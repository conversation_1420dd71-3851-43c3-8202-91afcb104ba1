{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2015", "module": "esnext", "lib": ["es2020", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@tz-mobile/shared/types": ["libs/shared/types/src/index.ts"], "@tz-mobile/shared/utils": ["libs/shared/utils/src/index.ts"], "@tz-mobile/shared/config": ["libs/shared/config/src/index.ts"], "@tz-mobile/core/auth": ["libs/core/auth/src/index.ts"], "@tz-mobile/core/state": ["libs/core/state/src/index.ts"], "@tz-mobile/core/network": ["libs/core/network/src/index.ts"], "@tz-mobile/ui/components": ["libs/ui/components/src/index.ts"], "@tz-mobile/ui/templates": ["libs/ui/templates/src/index.ts"], "@tz-mobile/ui/theme": ["libs/ui/theme/src/index.ts"], "@tz-mobile/feature/todo": ["libs/feature/todo/src/index.ts"], "@tz-mobile/feature/form": ["libs/feature/form/src/index.ts"], "@tz-mobile/feature/list": ["libs/feature/list/src/index.ts"], "@tz-mobile/feature/chart": ["libs/feature/chart/src/index.ts"], "@tz-mobile/utils/formatters": ["libs/utils/formatters/src/index.ts"], "@tz-mobile/utils/validators": ["libs/utils/validators/src/index.ts"], "@tz-mobile/utils/helpers": ["libs/utils/helpers/src/index.ts"]}, "allowJs": true, "strict": true, "noEmit": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "incremental": true, "plugins": [{"name": "next"}], "jsx": "preserve"}, "exclude": ["node_modules", "tmp"]}