{"name": "shared-config", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/shared/config/src", "projectType": "library", "tags": ["scope:shared", "type:config"], "targets": {"lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/shared/config/**/*.{ts,tsx,js,jsx}"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/shared/config/jest.config.ts", "passWithNoTests": true}, "configurations": {"ci": {"ci": true, "coverageReporters": ["text"]}}}}}