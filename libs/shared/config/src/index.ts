/**
 * 共享配置
 * 定义整个应用的配置常量和环境变量
 */

// 应用基础配置
export const APP_CONFIG = {
  name: 'TZ移动端底座',
  version: '1.0.0',
  description: '基于Nx和Next.js的移动端底座应用',
  author: 'TZ Team',
  homepage: 'https://github.com/tz-team/tz-mobile',
} as const;

// API配置
export const API_CONFIG = {
  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL || '/api',
  timeout: 10000,
  retryTimes: 3,
  retryDelay: 1000,
} as const;

// 认证配置
export const AUTH_CONFIG = {
  tokenKey: 'auth_token',
  refreshTokenKey: 'refresh_token',
  userKey: 'user_info',
  tokenExpireTime: 7 * 24 * 60 * 60 * 1000, // 7天
  refreshThreshold: 30 * 60 * 1000, // 30分钟
} as const;

// 存储配置
export const STORAGE_CONFIG = {
  prefix: 'tz_mobile_',
  version: '1.0',
  keys: {
    theme: 'theme',
    locale: 'locale',
    userPreferences: 'user_preferences',
    appSettings: 'app_settings',
  },
} as const;

// 主题配置
export const THEME_CONFIG = {
  defaultMode: 'light' as const,
  colors: {
    primary: '#1677ff',
    success: '#52c41a',
    warning: '#faad14',
    error: '#ff4d4f',
    info: '#1677ff',
  },
  breakpoints: {
    xs: '480px',
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px',
  },
} as const;

// 分页配置
export const PAGINATION_CONFIG = {
  defaultPageSize: 20,
  pageSizeOptions: [10, 20, 50, 100],
  showSizeChanger: true,
  showQuickJumper: true,
} as const;

// 表单配置
export const FORM_CONFIG = {
  validateTrigger: 'onChange' as const,
  labelAlign: 'left' as const,
  requiredMark: true,
  colon: true,
  scrollToFirstError: true,
} as const;

// 上传配置
export const UPLOAD_CONFIG = {
  maxSize: 10 * 1024 * 1024, // 10MB
  acceptTypes: {
    image: ['.jpg', '.jpeg', '.png', '.gif', '.webp'],
    document: ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'],
    archive: ['.zip', '.rar', '.7z'],
  },
  chunkSize: 1024 * 1024, // 1MB
} as const;

// 通知配置
export const NOTIFICATION_CONFIG = {
  duration: 3000,
  maxCount: 5,
  placement: 'topRight' as const,
  closable: true,
} as const;

// 路由配置
export const ROUTE_CONFIG = {
  basePath: '/',
  loginPath: '/login',
  homePath: '/',
  notFoundPath: '/404',
  errorPath: '/error',
} as const;

// 功能开关配置
export const FEATURE_FLAGS = {
  enableDarkMode: true,
  enableI18n: true,
  enablePWA: false,
  enableAnalytics: false,
  enableErrorBoundary: true,
  enableDevTools: process.env.NODE_ENV === 'development',
} as const;

// 性能配置
export const PERFORMANCE_CONFIG = {
  enableLazyLoading: true,
  enableCodeSplitting: true,
  enableServiceWorker: false,
  enablePreload: true,
  debounceDelay: 300,
  throttleDelay: 100,
} as const;

// 安全配置
export const SECURITY_CONFIG = {
  enableCSRF: true,
  enableXSS: true,
  enableContentSecurityPolicy: true,
  sessionTimeout: 30 * 60 * 1000, // 30分钟
  maxLoginAttempts: 5,
  lockoutDuration: 15 * 60 * 1000, // 15分钟
} as const;

// 日志配置
export const LOG_CONFIG = {
  level: process.env.NODE_ENV === 'development' ? 'debug' : 'error',
  enableConsole: process.env.NODE_ENV === 'development',
  enableRemote: process.env.NODE_ENV === 'production',
  maxLogSize: 1000,
} as const;

// 缓存配置
export const CACHE_CONFIG = {
  enableMemoryCache: true,
  enableLocalStorage: true,
  enableSessionStorage: true,
  defaultTTL: 5 * 60 * 1000, // 5分钟
  maxCacheSize: 100,
} as const;

// 网络配置
export const NETWORK_CONFIG = {
  enableRetry: true,
  enableOfflineMode: true,
  enableRequestQueue: true,
  maxConcurrentRequests: 6,
  requestTimeout: 30000,
} as const;

// 开发配置
export const DEV_CONFIG = {
  enableMockData: process.env.NODE_ENV === 'development',
  enableHotReload: process.env.NODE_ENV === 'development',
  enableSourceMap: process.env.NODE_ENV === 'development',
  enableDebugger: process.env.NODE_ENV === 'development',
} as const;

// 环境变量类型定义
export interface EnvironmentVariables {
  NODE_ENV: 'development' | 'production' | 'test';
  NEXT_PUBLIC_API_BASE_URL?: string;
  NEXT_PUBLIC_APP_VERSION?: string;
  NEXT_PUBLIC_ENABLE_ANALYTICS?: string;
  NEXT_PUBLIC_SENTRY_DSN?: string;
}

// 获取环境变量的工具函数
export const getEnvVar = (key: keyof EnvironmentVariables, defaultValue?: string): string => {
  return process.env[key] || defaultValue || '';
};

// 检查是否为开发环境
export const isDevelopment = (): boolean => {
  return process.env.NODE_ENV === 'development';
};

// 检查是否为生产环境
export const isProduction = (): boolean => {
  return process.env.NODE_ENV === 'production';
};

// 检查是否为测试环境
export const isTest = (): boolean => {
  return process.env.NODE_ENV === 'test';
};

// 导出所有配置
export * from './index';
