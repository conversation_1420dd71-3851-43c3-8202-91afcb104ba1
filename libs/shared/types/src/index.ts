/**
 * 共享类型定义
 * 定义整个应用中使用的通用类型
 */

// 基础响应类型
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  success: boolean;
  timestamp?: number;
}

// 分页参数类型
export interface PaginationParams {
  page: number;
  pageSize: number;
  total?: number;
}

// 分页响应类型
export interface PaginatedResponse<T> {
  list: T[];
  pagination: PaginationParams;
}

// 用户类型
export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  phone?: string;
  role?: string;
  status?: 'active' | 'inactive' | 'pending';
  createdAt?: string;
  updatedAt?: string;
}

// 认证相关类型
export interface AuthTokens {
  accessToken: string;
  refreshToken?: string;
  expiresIn?: number;
}

export interface LoginCredentials {
  email: string;
  password: string;
  remember?: boolean;
}

// 表单相关类型
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'number' | 'select' | 'textarea' | 'date' | 'checkbox' | 'radio';
  required?: boolean;
  placeholder?: string;
  defaultValue?: any;
  options?: Array<{ label: string; value: any }>;
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    message?: string;
  };
}

export interface FormConfig {
  title: string;
  description?: string;
  fields: FormField[];
  submitText?: string;
  resetText?: string;
}

// 列表相关类型
export interface ListColumn {
  key: string;
  title: string;
  dataIndex: string;
  width?: number;
  align?: 'left' | 'center' | 'right';
  sortable?: boolean;
  filterable?: boolean;
  render?: (value: any, record: any, index: number) => React.ReactNode;
}

export interface ListConfig {
  columns: ListColumn[];
  pagination?: boolean;
  selection?: boolean;
  actions?: Array<{
    key: string;
    label: string;
    type?: 'primary' | 'default' | 'danger';
    onClick: (record: any) => void;
  }>;
}

// 图表相关类型
export interface ChartData {
  name: string;
  value: number;
  color?: string;
}

export interface ChartConfig {
  type: 'line' | 'bar' | 'pie' | 'area';
  title?: string;
  data: ChartData[];
  xAxis?: string;
  yAxis?: string;
  colors?: string[];
}

// 待办事项类型
export interface TodoItem {
  id: string;
  title: string;
  description?: string;
  completed: boolean;
  priority: 'low' | 'medium' | 'high';
  dueDate?: string;
  tags?: string[];
  createdAt: string;
  updatedAt: string;
}

// 通知类型
export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title?: string;
  message: string;
  duration?: number;
  closable?: boolean;
  createdAt: string;
}

// 主题类型
export interface Theme {
  mode: 'light' | 'dark';
  primaryColor: string;
  colors: {
    [key: string]: string;
  };
}

// 应用配置类型
export interface AppConfig {
  name: string;
  version: string;
  apiBaseUrl: string;
  theme: Theme;
  features: {
    [key: string]: boolean;
  };
}

// 错误类型
export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: string;
}

// 路由类型
export interface Route {
  path: string;
  name: string;
  component?: React.ComponentType;
  icon?: React.ReactNode;
  children?: Route[];
  meta?: {
    title?: string;
    requireAuth?: boolean;
    permissions?: string[];
  };
}

// 菜单项类型
export interface MenuItem {
  key: string;
  label: string;
  icon?: React.ReactNode;
  path?: string;
  children?: MenuItem[];
  disabled?: boolean;
}

// 工具函数类型
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

// 事件类型
export interface AppEvent<T = any> {
  type: string;
  payload: T;
  timestamp: number;
}

// 导出所有类型
export * from './index';
