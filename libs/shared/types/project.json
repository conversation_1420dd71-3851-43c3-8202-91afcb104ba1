{"name": "shared-types", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/shared/types/src", "projectType": "library", "tags": ["scope:shared", "type:types"], "targets": {"lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/shared/types/**/*.{ts,tsx,js,jsx}"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/shared/types/jest.config.ts", "passWithNoTests": true}, "configurations": {"ci": {"ci": true, "coverageReporters": ["text"]}}}}}