{"name": "shared-utils", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/shared/utils/src", "projectType": "library", "tags": ["scope:shared", "type:utils"], "targets": {"lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/shared/utils/**/*.{ts,tsx,js,jsx}"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/shared/utils/jest.config.ts", "passWithNoTests": true}, "configurations": {"ci": {"ci": true, "coverageReporters": ["text"]}}}}}