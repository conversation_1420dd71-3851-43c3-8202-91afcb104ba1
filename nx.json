{"$schema": "./node_modules/nx/schemas/nx-schema.json", "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default", "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)", "!{projectRoot}/tsconfig.spec.json", "!{projectRoot}/jest.config.[jt]s", "!{projectRoot}/src/test-setup.[jt]s", "!{projectRoot}/test-setup.[jt]s", "!{projectRoot}/.eslintrc.json", "!{projectRoot}/eslint.config.js"], "sharedGlobals": []}, "targetDefaults": {"build": {"dependsOn": ["^build"], "inputs": ["production", "^production"], "cache": true}, "lint": {"inputs": ["default", "{workspaceRoot}/.eslintrc.json", "{workspaceRoot}/.eslintignore", "{workspaceRoot}/eslint.config.js"], "cache": true}, "test": {"inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"], "cache": true}, "e2e": {"inputs": ["default", "^production"], "cache": true}}, "workspaceLayout": {"appsDir": "apps", "libsDir": "libs"}, "generators": {"@nx/next": {"application": {"style": "css", "linter": "eslint"}}, "@nx/react": {"application": {"babel": true}}}, "plugins": [{"plugin": "@nx/next/plugin", "options": {"startTargetName": "start", "buildTargetName": "build", "devTargetName": "dev", "serveStaticTargetName": "serve-static"}}, {"plugin": "@nx/eslint/plugin", "options": {"targetName": "lint"}}]}