{"name": "mobile-app", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/mobile-app", "projectType": "application", "tags": ["scope:mobile", "type:app"], "targets": {"build": {"executor": "@nx/next:build", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"outputPath": "dist/apps/mobile-app"}, "configurations": {"development": {"outputPath": "apps/mobile-app"}, "production": {}}}, "serve": {"executor": "@nx/next:server", "defaultConfiguration": "development", "options": {"buildTarget": "mobile-app:build", "dev": true, "port": 3000}, "configurations": {"development": {"buildTarget": "mobile-app:build:development", "dev": true}, "production": {"buildTarget": "mobile-app:build:production", "dev": false}}}, "dev": {"executor": "@nx/next:server", "options": {"buildTarget": "mobile-app:build:development", "dev": true, "port": 3000}}, "export": {"executor": "@nx/next:export", "options": {"buildTarget": "mobile-app:build:production"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/mobile-app/**/*.{ts,tsx,js,jsx}"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/mobile-app/jest.config.ts", "passWithNoTests": true}, "configurations": {"ci": {"ci": true, "coverageReporters": ["text"]}}}}}