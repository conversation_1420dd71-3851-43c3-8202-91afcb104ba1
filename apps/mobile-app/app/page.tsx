'use client';

import React from 'react';
import { Button, Card, Space, Grid, Badge } from 'antd-mobile';
import {
  AppOutline,
  SetOutline,
  UserOutline,
  GlobalOutline,
  FileOutline,
  PieOutline,
  CheckCircleOutline,
  UnorderedListOutline
} from 'antd-mobile-icons';
import { useGlobalUIStore, useAuthStore } from './store';

// 移动端底座首页
export default function Home() {
  const { addNotification, toggleTheme, themeMode } = useGlobalUIStore();
  const { isAuthenticated, login, logout } = useAuthStore();

  // 演示功能
  const handleShowNotification = (type: 'success' | 'error' | 'warning' | 'info') => {
    addNotification({
      type,
      message: `这是一个${type}类型的通知消息`,
    });
  };

  const handleLogin = () => {
    login(
      {
        id: '1',
        name: '演示用户',
        email: '<EMAIL>',
        avatar: 'https://via.placeholder.com/40',
      },
      'demo-token',
      ['read', 'write', 'admin']
    );
    addNotification({
      type: 'success',
      message: '登录成功！',
    });
  };

  const handleLogout = () => {
    logout();
    addNotification({
      type: 'info',
      message: '已退出登录',
    });
  };

  // 功能模块配置
  const modules = [
    {
      title: '表单模块',
      icon: <FileOutline />,
      description: '动态表单生成和验证',
      path: '/form',
      color: '#1677ff',
    },
    {
      title: '列表模块',
      icon: <UnorderedListOutline />,
      description: '数据列表展示和操作',
      path: '/list',
      color: '#52c41a',
    },
    {
      title: '图表模块',
      icon: <PieOutline />,
      description: '数据可视化图表',
      path: '/chart',
      color: '#faad14',
    },
    {
      title: '待办模块',
      icon: <CheckCircleOutline />,
      description: '任务管理和跟踪',
      path: '/todo',
      color: '#722ed1',
    },
  ];

  return (
    <div className="p-4 space-y-4">
      {/* 头部信息 */}
      <Card className="bg-gradient-to-r from-blue-500 to-purple-600 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-xl font-bold mb-1">TZ移动端底座</h1>
            <p className="text-blue-100 text-sm">
              基于Nx + Next.js + Ant Design Mobile
            </p>
          </div>
          <AppOutline fontSize={32} />
        </div>
      </Card>

      {/* 用户状态 */}
      <Card>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <UserOutline fontSize={24} />
            <div>
              <div className="font-medium">
                {isAuthenticated ? '演示用户' : '未登录'}
              </div>
              <div className="text-sm text-gray-500">
                {isAuthenticated ? '<EMAIL>' : '点击登录体验功能'}
              </div>
            </div>
          </div>
          <Button
            size="small"
            color={isAuthenticated ? 'danger' : 'primary'}
            onClick={isAuthenticated ? handleLogout : handleLogin}
          >
            {isAuthenticated ? '退出' : '登录'}
          </Button>
        </div>
      </Card>

      {/* 功能模块 */}
      <Card title="功能模块">
        <Grid columns={2} gap={12}>
          {modules.map((module, index) => (
            <Card
              key={index}
              className="text-center border border-gray-200 hover:border-blue-300 transition-colors"
            >
              <div className="flex flex-col items-center space-y-2">
                <div
                  className="w-12 h-12 rounded-full flex items-center justify-center text-white text-xl"
                  style={{ backgroundColor: module.color }}
                >
                  {module.icon}
                </div>
                <div className="font-medium">{module.title}</div>
                <div className="text-xs text-gray-500 text-center">
                  {module.description}
                </div>
                <Badge content="开发中" color="#ff4d4f">
                  <Button size="small" color="primary" fill="outline">
                    进入
                  </Button>
                </Badge>
              </div>
            </Card>
          ))}
        </Grid>
      </Card>

      {/* 系统功能演示 */}
      <Card title="系统功能演示">
        <Space direction="vertical" block>
          <div className="flex items-center justify-between">
            <span>主题模式</span>
            <Button
              size="small"
              onClick={toggleTheme}
              color="primary"
              fill="outline"
            >
              <GlobalOutline className="mr-1" />
              {themeMode === 'light' ? '切换到暗色' : '切换到亮色'}
            </Button>
          </div>

          <div className="border-t pt-3">
            <div className="text-sm text-gray-600 mb-2">通知消息演示：</div>
            <Space wrap>
              <Button
                size="small"
                color="success"
                onClick={() => handleShowNotification('success')}
              >
                成功
              </Button>
              <Button
                size="small"
                color="danger"
                onClick={() => handleShowNotification('error')}
              >
                错误
              </Button>
              <Button
                size="small"
                color="warning"
                onClick={() => handleShowNotification('warning')}
              >
                警告
              </Button>
              <Button
                size="small"
                color="primary"
                onClick={() => handleShowNotification('info')}
              >
                信息
              </Button>
            </Space>
          </div>
        </Space>
      </Card>

      {/* 技术栈信息 */}
      <Card title="技术栈">
        <div className="grid grid-cols-2 gap-3 text-sm">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
            <span>Next.js 15</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span>React 19</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
            <span>Nx Workspace</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
            <span>Ant Design Mobile</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-cyan-500 rounded-full"></div>
            <span>TailwindCSS</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-red-500 rounded-full"></div>
            <span>Zustand</span>
          </div>
        </div>
      </Card>
    </div>
  );
}
