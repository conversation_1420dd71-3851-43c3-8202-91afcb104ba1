import { render, screen, fireEvent } from '@testing-library/react';
import Home from '../page';

// Mock Zustand stores
jest.mock('../store', () => ({
  useGlobalUIStore: () => ({
    addNotification: jest.fn(),
    toggleTheme: jest.fn(),
    themeMode: 'light',
  }),
  useAuthStore: () => ({
    isAuthenticated: false,
    login: jest.fn(),
    logout: jest.fn(),
  }),
}));

describe('Home Page', () => {
  it('renders the main heading', () => {
    render(<Home />);
    
    const heading = screen.getByText('TZ移动端底座');
    expect(heading).toBeInTheDocument();
  });

  it('renders the technology stack section', () => {
    render(<Home />);
    
    const techStackSection = screen.getByText('技术栈');
    expect(techStackSection).toBeInTheDocument();
    
    // Check for specific technologies
    expect(screen.getByText('Next.js 15')).toBeInTheDocument();
    expect(screen.getByText('React 19')).toBeInTheDocument();
    expect(screen.getByText('Nx Workspace')).toBeInTheDocument();
    expect(screen.getByText('Ant Design Mobile')).toBeInTheDocument();
    expect(screen.getByText('TailwindCSS')).toBeInTheDocument();
    expect(screen.getByText('Zustand')).toBeInTheDocument();
  });

  it('renders feature modules', () => {
    render(<Home />);
    
    expect(screen.getByText('表单模块')).toBeInTheDocument();
    expect(screen.getByText('列表模块')).toBeInTheDocument();
    expect(screen.getByText('图表模块')).toBeInTheDocument();
    expect(screen.getByText('待办模块')).toBeInTheDocument();
  });

  it('renders notification demo buttons', () => {
    render(<Home />);
    
    expect(screen.getByText('成功')).toBeInTheDocument();
    expect(screen.getByText('错误')).toBeInTheDocument();
    expect(screen.getByText('警告')).toBeInTheDocument();
    expect(screen.getByText('信息')).toBeInTheDocument();
  });

  it('shows login button when not authenticated', () => {
    render(<Home />);
    
    const loginButton = screen.getByText('登录');
    expect(loginButton).toBeInTheDocument();
  });
});
