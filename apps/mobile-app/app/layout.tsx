import './globals.css';
import 'antd-mobile/es/global';
import type { Metadata } from 'next';
import { AppProviders } from './providers';

export const metadata: Metadata = {
  title: 'TZ移动端底座',
  description: '基于Nx和Next.js的移动端底座应用',
  keywords: ['移动端', '底座', 'Next.js', 'Nx', 'React'],
  authors: [{ name: 'TZ Team' }],
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN" suppressHydrationWarning>
      <body className="pb-14 antialiased">
        <AppProviders>
          <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-200">
            {children}
          </div>
        </AppProviders>
      </body>
    </html>
  );
}
