/**
 * 移动端底座状态管理
 * 基于Zustand的状态管理系统
 */

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

// 全局UI状态接口
interface GlobalUIState {
  // 加载状态
  loading: boolean;
  // 通知消息
  notifications: Array<{
    id: string;
    type: 'success' | 'error' | 'warning' | 'info';
    message: string;
    duration?: number;
  }>;
  // 模态框状态
  modals: {
    [key: string]: boolean;
  };
  // 主题模式
  themeMode: 'light' | 'dark';
  // 语言设置
  locale: 'zh-CN' | 'en-US';
}

// 全局UI状态操作接口
interface GlobalUIActions {
  // 设置加载状态
  setLoading: (loading: boolean) => void;
  // 添加通知
  addNotification: (notification: Omit<GlobalUIState['notifications'][0], 'id'>) => void;
  // 移除通知
  removeNotification: (id: string) => void;
  // 清空通知
  clearNotifications: () => void;
  // 设置模态框状态
  setModal: (key: string, visible: boolean) => void;
  // 切换主题模式
  toggleTheme: () => void;
  // 设置语言
  setLocale: (locale: GlobalUIState['locale']) => void;
}

// 全局UI状态Store
export const useGlobalUIStore = create<GlobalUIState & GlobalUIActions>()(
  devtools(
    persist(
      (set, get) => ({
        // 初始状态
        loading: false,
        notifications: [],
        modals: {},
        themeMode: 'light',
        locale: 'zh-CN',

        // 操作方法
        setLoading: (loading) => set({ loading }),

        addNotification: (notification) => {
          const id = Date.now().toString();
          set((state) => ({
            notifications: [...state.notifications, { ...notification, id }],
          }));
          
          // 自动移除通知
          if (notification.duration !== 0) {
            setTimeout(() => {
              get().removeNotification(id);
            }, notification.duration || 3000);
          }
        },

        removeNotification: (id) =>
          set((state) => ({
            notifications: state.notifications.filter((n) => n.id !== id),
          })),

        clearNotifications: () => set({ notifications: [] }),

        setModal: (key, visible) =>
          set((state) => ({
            modals: { ...state.modals, [key]: visible },
          })),

        toggleTheme: () =>
          set((state) => ({
            themeMode: state.themeMode === 'light' ? 'dark' : 'light',
          })),

        setLocale: (locale) => set({ locale }),
      }),
      {
        name: 'global-ui-store',
        // 只持久化部分状态
        partialize: (state) => ({
          themeMode: state.themeMode,
          locale: state.locale,
        }),
      }
    ),
    {
      name: 'global-ui-store',
    }
  )
);

// 认证状态接口
interface AuthState {
  // 用户信息
  user: {
    id: string;
    name: string;
    email: string;
    avatar?: string;
  } | null;
  // 认证token
  token: string | null;
  // 是否已认证
  isAuthenticated: boolean;
  // 权限列表
  permissions: string[];
}

// 认证操作接口
interface AuthActions {
  // 登录
  login: (user: AuthState['user'], token: string, permissions?: string[]) => void;
  // 登出
  logout: () => void;
  // 更新用户信息
  updateUser: (user: Partial<AuthState['user']>) => void;
  // 检查权限
  hasPermission: (permission: string) => boolean;
}

// 认证状态Store
export const useAuthStore = create<AuthState & AuthActions>()(
  devtools(
    persist(
      (set, get) => ({
        // 初始状态
        user: null,
        token: null,
        isAuthenticated: false,
        permissions: [],

        // 操作方法
        login: (user, token, permissions = []) =>
          set({
            user,
            token,
            isAuthenticated: true,
            permissions,
          }),

        logout: () =>
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            permissions: [],
          }),

        updateUser: (userData) =>
          set((state) => ({
            user: state.user ? { ...state.user, ...userData } : null,
          })),

        hasPermission: (permission) => {
          const { permissions } = get();
          return permissions.includes(permission);
        },
      }),
      {
        name: 'auth-store',
      }
    ),
    {
      name: 'auth-store',
    }
  )
);

// 导出所有Store
export * from './index';
