/**
 * 移动端底座主题配置
 * 基于Ant Design Mobile和TailwindCSS的主题系统
 */

// Ant Design Mobile 主题配置
export const antdMobileTheme = {
  // 主色调配置
  primaryColor: '#1677ff',
  // 成功色
  successColor: '#52c41a',
  // 警告色
  warningColor: '#faad14',
  // 错误色
  errorColor: '#ff4d4f',
  // 信息色
  infoColor: '#1677ff',
  // 文本色
  textColor: '#000000',
  // 次要文本色
  textColorSecondary: '#666666',
  // 禁用文本色
  textColorDisabled: '#cccccc',
  // 边框色
  borderColor: '#d9d9d9',
  // 背景色
  backgroundColor: '#ffffff',
  // 组件背景色
  componentBackground: '#ffffff',
  // 页面背景色
  bodyBackground: '#f5f5f5',
};

// TailwindCSS 主题变量
export const tailwindTheme = {
  colors: {
    primary: {
      50: '#eff6ff',
      100: '#dbeafe',
      200: '#bfdbfe',
      300: '#93c5fd',
      400: '#60a5fa',
      500: '#3b82f6',
      600: '#2563eb',
      700: '#1d4ed8',
      800: '#1e40af',
      900: '#1e3a8a',
    },
    gray: {
      50: '#f9fafb',
      100: '#f3f4f6',
      200: '#e5e7eb',
      300: '#d1d5db',
      400: '#9ca3af',
      500: '#6b7280',
      600: '#4b5563',
      700: '#374151',
      800: '#1f2937',
      900: '#111827',
    },
  },
  spacing: {
    xs: '4px',
    sm: '8px',
    md: '16px',
    lg: '24px',
    xl: '32px',
    '2xl': '48px',
  },
  borderRadius: {
    sm: '4px',
    md: '8px',
    lg: '12px',
    xl: '16px',
  },
};

// 响应式断点
export const breakpoints = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px',
};

// 导出默认主题
export const defaultTheme = {
  antdMobile: antdMobileTheme,
  tailwind: tailwindTheme,
  breakpoints,
};
