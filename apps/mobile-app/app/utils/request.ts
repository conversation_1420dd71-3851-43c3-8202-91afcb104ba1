/**
 * 移动端底座网络请求层
 * 基于Axios的网络请求封装
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { useAuthStore } from '../store';
import { useGlobalUIStore } from '../store';

// 请求配置接口
interface RequestConfig extends AxiosRequestConfig {
  // 是否显示加载状态
  showLoading?: boolean;
  // 是否显示错误提示
  showError?: boolean;
  // 自定义错误处理
  customErrorHandler?: (error: any) => void;
}

// 响应数据接口
interface ResponseData<T = any> {
  code: number;
  message: string;
  data: T;
  success: boolean;
}

// 创建Axios实例
const createAxiosInstance = (): AxiosInstance => {
  const instance = axios.create({
    baseURL: process.env.NEXT_PUBLIC_API_BASE_URL || '/api',
    timeout: 10000,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  // 请求拦截器
  instance.interceptors.request.use(
    (config) => {
      // 添加认证token
      const { token } = useAuthStore.getState();
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }

      // 显示加载状态
      const requestConfig = config as RequestConfig;
      if (requestConfig.showLoading !== false) {
        useGlobalUIStore.getState().setLoading(true);
      }

      return config;
    },
    (error) => {
      useGlobalUIStore.getState().setLoading(false);
      return Promise.reject(error);
    }
  );

  // 响应拦截器
  instance.interceptors.response.use(
    (response: AxiosResponse<ResponseData>) => {
      // 隐藏加载状态
      useGlobalUIStore.getState().setLoading(false);

      const { data } = response;
      
      // 检查业务状态码
      if (data.success === false || data.code !== 200) {
        const error = new Error(data.message || '请求失败');
        return Promise.reject(error);
      }

      return response;
    },
    (error) => {
      // 隐藏加载状态
      useGlobalUIStore.getState().setLoading(false);

      // 处理HTTP状态码错误
      const { response } = error;
      let message = '网络请求失败';

      if (response) {
        switch (response.status) {
          case 401:
            message = '未授权，请重新登录';
            // 清除认证信息
            useAuthStore.getState().logout();
            break;
          case 403:
            message = '拒绝访问';
            break;
          case 404:
            message = '请求地址不存在';
            break;
          case 500:
            message = '服务器内部错误';
            break;
          default:
            message = response.data?.message || '请求失败';
        }
      } else if (error.code === 'ECONNABORTED') {
        message = '请求超时';
      } else if (error.message === 'Network Error') {
        message = '网络连接失败';
      }

      // 显示错误提示
      const requestConfig = error.config as RequestConfig;
      if (requestConfig?.showError !== false) {
        useGlobalUIStore.getState().addNotification({
          type: 'error',
          message,
        });
      }

      // 自定义错误处理
      if (requestConfig?.customErrorHandler) {
        requestConfig.customErrorHandler(error);
      }

      return Promise.reject(error);
    }
  );

  return instance;
};

// 创建请求实例
const request = createAxiosInstance();

// 封装请求方法
export const api = {
  // GET请求
  get: <T = any>(url: string, config?: RequestConfig): Promise<T> => {
    return request.get<ResponseData<T>>(url, config).then(res => res.data.data);
  },

  // POST请求
  post: <T = any>(url: string, data?: any, config?: RequestConfig): Promise<T> => {
    return request.post<ResponseData<T>>(url, data, config).then(res => res.data.data);
  },

  // PUT请求
  put: <T = any>(url: string, data?: any, config?: RequestConfig): Promise<T> => {
    return request.put<ResponseData<T>>(url, data, config).then(res => res.data.data);
  },

  // DELETE请求
  delete: <T = any>(url: string, config?: RequestConfig): Promise<T> => {
    return request.delete<ResponseData<T>>(url, config).then(res => res.data.data);
  },

  // PATCH请求
  patch: <T = any>(url: string, data?: any, config?: RequestConfig): Promise<T> => {
    return request.patch<ResponseData<T>>(url, data, config).then(res => res.data.data);
  },
};

// 导出原始axios实例（用于特殊需求）
export { request };

// 导出类型
export type { RequestConfig, ResponseData };
