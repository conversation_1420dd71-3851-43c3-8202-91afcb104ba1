import type { NextConfig } from "next";
import { withNx } from "@nx/next/plugins/with-nx";

const nextConfig: NextConfig = {
  /* config options here */
  reactStrictMode: false, // 禁用React严格模式，避免组件重复渲染
  nx: {
    // Set this to true if you use SVG imports
    svgr: false,
  },
  // 支持从workspace库导入
  transpilePackages: [],
  experimental: {
    // 启用App Router
    appDir: true,
  },
};

export default withNx(nextConfig);
