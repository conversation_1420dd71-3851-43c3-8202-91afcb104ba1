{"name": "mobile-framework", "version": "0.1.0", "private": true, "scripts": {"dev": "nx serve mobile-app", "build": "nx build mobile-app", "start": "nx start mobile-app", "lint": "nx lint mobile-app", "test": "nx test mobile-app", "nx": "nx", "graph": "nx graph", "affected": "nx affected"}, "dependencies": {"@types/uuid": "^10.0.0", "antd-mobile": "^5.39.0", "antd-mobile-icons": "^0.3.0", "axios": "^1.9.0", "next": "15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "tslib": "^2.8.1", "uuid": "^11.1.0", "zustand": "^5.0.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@nx/eslint": "^21.1.2", "@nx/eslint-plugin": "^21.1.2", "@nx/jest": "^21.1.2", "@nx/next": "^21.1.2", "@nx/workspace": "^21.1.2", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "eslint-config-prettier": "^10.1.5", "jest": "30.0.0-beta.3", "jest-environment-jsdom": "30.0.0-beta.3", "nx": "^21.1.2", "prettier": "^3.5.3", "tailwindcss": "^4", "ts-jest": "^29.3.4", "ts-node": "^10.9.2", "typescript": "^5"}}