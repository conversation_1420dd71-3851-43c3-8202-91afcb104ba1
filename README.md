# tz-mobile

这是一个使用 [Next.js](https://nextjs.org) 构建的项目，使用 [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app) 脚手架创建。

## 开发指南

首先，运行开发服务器：

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

打开 [http://localhost:3000](http://localhost:3000) 查看结果。

您可以通过修改 `app/page.tsx` 来开始编辑页面。页面会随着您编辑文件而自动更新。

本项目使用 [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) 自动优化并加载 [Geist](https://vercel.com/font)，这是 Vercel 的一个新字体系列。

## 了解更多

要了解有关 Next.js 的更多信息，请查看以下资源：

- [Next.js 文档](https://nextjs.org/docs) - 了解 Next.js 功能和 API。
- [学习 Next.js](https://nextjs.org/learn) - 一个交互式 Next.js 教程。

您可以查看 [Next.js GitHub 仓库](https://github.com/vercel/next.js) - 欢迎您的反馈和贡献！

## GitLab 协作指南

### 添加文件

- [创建](https://docs.gitlab.com/ee/user/project/repository/web_editor.html#create-a-file) 或 [上传](https://docs.gitlab.com/ee/user/project/repository/web_editor.html#upload-a-file) 文件
- [使用命令行添加文件](https://docs.gitlab.com/ee/gitlab-basics/add-file.html#add-a-file-using-the-command-line) 或使用以下命令推送现有 Git 仓库：

```
cd existing_repo
git remote <NAME_EMAIL>:it-frontend/tz-mobile.git
git branch -M main
git push -uf origin main
```

### 与团队协作

- [邀请团队成员和协作者](https://docs.gitlab.com/ee/user/project/members/)
- [创建新的合并请求](https://docs.gitlab.com/ee/user/project/merge_requests/creating_merge_requests.html)
- [从合并请求自动关闭问题](https://docs.gitlab.com/ee/user/project/issues/managing_issues.html#closing-issues-automatically)
- [启用合并请求审批](https://docs.gitlab.com/ee/user/project/merge_requests/approvals/)
- [设置自动合并](https://docs.gitlab.com/ee/user/project/merge_requests/merge_when_pipeline_succeeds.html)

### 测试和部署

使用 GitLab 内置的持续集成：

- [开始使用 GitLab CI/CD](https://docs.gitlab.com/ee/ci/quick_start/index.html)
- [使用静态应用程序安全测试（SAST）分析代码中的已知漏洞](https://docs.gitlab.com/ee/user/application_security/sast/)
- [部署到 Kubernetes、Amazon EC2 或 Amazon ECS，使用自动部署](https://docs.gitlab.com/ee/topics/autodevops/requirements.html)
- [使用基于拉取的部署改进 Kubernetes 管理](https://docs.gitlab.com/ee/user/clusters/agent/)
- [设置受保护的环境](https://docs.gitlab.com/ee/ci/environments/protected_environments.html)
