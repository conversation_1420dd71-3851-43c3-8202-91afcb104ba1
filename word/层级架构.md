# 移动端底座层级架构图

## 1. 系统分层架构图

```mermaid
%%{init: {'theme': 'base', 'themeVariables': { 'primaryColor': '#a8c0d6', 'primaryTextColor': '#2c3e50', 'primaryBorderColor': '#6d8cb0', 'lineColor': '#6d8cb0', 'secondaryColor': '#8fb0a9', 'tertiaryColor': '#f9f9f9' }}}%%
graph TB
    User[用户] -->|交互| Presentation[表现层]
    
    subgraph 前端底座架构
        Presentation -->|依赖| BusinessLogic[业务逻辑层]
        BusinessLogic -->|依赖| DataAccess[数据访问层]
        Presentation -->|提供| PluginInterfaceP[插件接口]
        BusinessLogic -->|提供| PluginInterfaceB[插件接口]
        DataAccess -->|提供| PluginInterfaceD[插件接口]
        
        subgraph 表现层
            UI[用户界面组件] --> Shell[应用外壳]
            UI --> Navigation[路由]
            UI --> ListPage[业务列表]
            UI --> DetailPage[业务详情]
            UI --> ChartPage[业务图表]
            UI --> Modals[模态对话框]
            UI --> Notifications[通知提示]
            PluginInterfaceP --> UI
        end
        
        subgraph 业务逻辑层
            State[状态管理] --> AuthState[认证状态]
            State --> UIState[UI状态]
            State --> FeatureState[功能状态]
            State --> ThemeState[主题状态]
            
            Logic[业务逻辑] --> Validation[数据验证]
            Logic --> Processing[数据处理]
            Logic --> Workflow[工作流控制]
            PluginInterfaceB --> Logic
        end
        
        subgraph 数据访问层
            APILayer[API层] --> Authentication[认证处理]
            APILayer --> Caching[缓存策略]
            APILayer --> ErrorHandling[错误处理]
            
            LocalStorage[本地存储] --> SessionStorage[会话存储]
            LocalStorage --> PersistentStorage[持久化存储]
            PluginInterfaceD --> APILayer
        end
    end
    
    DataAccess -->|请求| Backend[后端服务]
    
    subgraph 后端服务
        Backend -->|包含| Auth[认证服务]
        Backend -->|包含| API[API网关]
        Backend -->|包含| Services[业务服务]
    end
    
    classDef user fill:#e2a4a4,stroke:#d98c8c,stroke-width:2px,color:#4d3939;
    classDef frontendLayer fill:#e8f0f7,stroke:#a8c0d6,stroke-width:2px,color:#3a4a5c;
    classDef presentationComponents fill:#b0c4d6,stroke:#8fa5b7,stroke-width:2px,color:#2c3e50;
    classDef businessLogicComponents fill:#d6c0b0,stroke:#b7a58f,stroke-width:2px,color:#3e342c;
    classDef dataAccessComponents fill:#b5add3,stroke:#9e95bb,stroke-width:2px,color:#2c3e50;
    classDef backendComponents fill:#f7e8aa,stroke:#d9cb94,stroke-width:2px,color:#4d452d;
    classDef pluginInterface fill:#cae3ca,stroke:#97c397,stroke-width:2px,color:#2e3e2e;
    
    class User user;
    class Presentation,BusinessLogic,DataAccess frontendLayer;
    class UI,Shell,Navigation,Pages,Modals,Notifications,FormTemplates,ListTemplates,ChartTemplates,Feedback presentationComponents;
    class State,AuthState,UIState,FeatureState,ThemeState,Logic,Validation,Processing,Workflow businessLogicComponents;
    class APILayer,Authentication,Caching,ErrorHandling,Retry,LocalStorage,SessionStorage,PersistentStorage dataAccessComponents;
    class Backend,Auth,API,Services backendComponents;
    class PluginInterfaceP,PluginInterfaceB,PluginInterfaceD pluginInterface;
```

## 2. 组件层级架构图

```mermaid
%%{init: {'theme': 'base', 'themeVariables': { 'primaryColor': '#b5add3', 'primaryTextColor': '#2c3e50', 'primaryBorderColor': '#9e95bb', 'lineColor': '#9e95bb', 'secondaryColor': '#d6c0b0', 'tertiaryColor': '#f9f9f9' }}}%%
graph TB

        CommonComponents[组件] --> BaseComponents[基础组件]
        CommonComponents --> CompoundComponents[复合组件]
        
        BaseComponents --> Button[按钮]
        BaseComponents --> Input[输入框]
        BaseComponents --> Icons[图标]
        BaseComponents --> Typography[布局]
        
        CompoundComponents --> Form[表单组件]
        CompoundComponents --> Lists[列表组件]
        CompoundComponents --> Charts[图表组件]
        CompoundComponents --> BusinessComponents[业务组件]
        
        Form --> FormTemplates[表单模板]
        Lists --> ListTemplates[列表模板]
        Charts --> ChartTemplates[图表模板]

    
    classDef appRoot fill:#a8c0d6,stroke:#8fa5b7,stroke-width:2px,color:#2c3e50;
    classDef providers fill:#b5add3,stroke:#9e95bb,stroke-width:2px,color:#2c3e50;
    classDef shellComponents fill:#d6c0b0,stroke:#b7a58f,stroke-width:2px,color:#3e342c;
    classDef layoutComponents fill:#e8f0f7,stroke:#a8c0d6,stroke-width:2px,color:#3a4a5c;
    classDef routeComponents fill:#f7e8aa,stroke:#d9cb94,stroke-width:2px,color:#4d452d;
    classDef commonComponents fill:#a8d1ce,stroke:#89b0ad,stroke-width:2px,color:#2d3e3d;
    
    class App appRoot;
    class AppProviders,AuthProvider,ThemeProvider,StoreProvider providers;
    class AppShell,GlobalModals,GlobalNotifications shellComponents;
    class Layout,Header,Navigation,Main,Footer layoutComponents;
    class Routes,AuthRoutes,AppRoutes,DashboardPage,TodoModule,Module1,Module2,SettingsPage,List1Page,Detail1Page,List2Page,Detail2Page,ChartPage routeComponents;
    class CommonComponents,BaseComponents,CompoundComponents,Button,Input,Icons,Typography,Form,Lists,Charts,Cards,FormTemplates,ListTemplates,ChartTemplates commonComponents;
```

## 3. 状态管理层级图

```mermaid
%%{init: {'theme': 'base', 'themeVariables': { 'primaryColor': '#a8c0d6', 'primaryTextColor': '#2c3e50', 'primaryBorderColor': '#8fa5b7', 'lineColor': '#8fa5b7', 'secondaryColor': '#d6c0b0', 'tertiaryColor': '#f9f9f9' }}}%%
graph TB
    StoreLayer[Zustand状态层] --> GlobalStores[全局状态]
    StoreLayer --> FeatureStores[功能状态]
    StoreLayer --> UIStores[UI状态]
    
    GlobalStores --> AuthStore[认证状态]
    GlobalStores --> ThemeStore[主题状态]
    GlobalStores --> UserStore[用户状态]
    
    FeatureStores --> Module1Store[模块1状态]
    FeatureStores --> Module2Store[模块2状态]
    FeatureStores --> TodoStore[代办状态]
    
    UIStores --> GlobalUIStore[全局UI状态]
    UIStores --> FormStore[表单状态]
    UIStores --> ListStore[列表状态]
    
    GlobalUIStore --> LoadingState[加载状态]
    GlobalUIStore --> NotificationState[通知状态]
    GlobalUIStore --> ModalState[模态框状态]
    
    AuthStore --> UserInfo[用户信息]

    
    subgraph 状态操作
        Components[组件层] -->|调用| Actions[状态操作]
        Actions -->|修改| State[状态]
        State -->|通知| Components
    end
    
    subgraph 持久化层
        PersistState[持久化状态] --> LocalStorage[本地存储]
        PersistState --> SessionStorage[会话存储]
        PersistState --> Cookie[Cookie存储]
    end
    
    GlobalStores -->|选择性持久化| PersistState

    Module1Store --> |更新| Components
    Module2Store --> |更新| Components
    
    classDef storeLayer fill:#a8c0d6,stroke:#8fa5b7,stroke-width:2px,color:#2c3e50;
    classDef globalStores fill:#b5add3,stroke:#9e95bb,stroke-width:2px,color:#2c3e50;
    classDef featureStores fill:#d6c0b0,stroke:#b7a58f,stroke-width:2px,color:#3e342c;
    classDef uiStores fill:#e8f0f7,stroke:#a8c0d6,stroke-width:2px,color:#3a4a5c;
    classDef stateDetails fill:#f7e8aa,stroke:#d9cb94,stroke-width:2px,color:#4d452d;
    classDef stateFlow fill:#a8d1ce,stroke:#89b0ad,stroke-width:2px,color:#2d3e3d;
    classDef persistLayer fill:#f9f9f9,stroke:#d9d9d9,stroke-width:2px,color:#2c3e50;
    
    class StoreLayer storeLayer;
    class GlobalStores,AuthStore,ThemeStore,UserStore globalStores;
    class FeatureStores,Module1Store,Module2Store,TodoStore featureStores;
    class UIStores,GlobalUIStore,FormStore,ListStore uiStores;
    class LoadingState,NotificationState,ModalState,UserInfo,Permissions,AuthStatus stateDetails;
    class Components,Actions,State stateFlow;
    class PersistState,LocalStorage,SessionStorage,Cookie persistLayer;
```

## 4. 技术栈层级图（集成Nx）

```mermaid
%%{init: {'theme': 'base', 'themeVariables': { 'primaryColor': '#b5add3', 'primaryTextColor': '#2c3e50', 'primaryBorderColor': '#9e95bb', 'lineColor': '#9e95bb', 'secondaryColor': '#d6c0b0', 'tertiaryColor': '#f9f9f9' }}}%%
graph TB
    Frontend[前端技术栈] --> Framework[框架层]
    Frontend --> UILayer[UI层]
    Frontend --> StateLayer[状态层]
    Frontend --> NetworkLayer[网络层]
    Frontend --> UtilityLayer[工具层]
    Frontend --> AuthLayer[认证层]
    Frontend --> Monitoring[监控层]
    Frontend -->  Development[开发工具]
    Frontend --> BuildTools[构建工具]
    
    Framework --> NextJS[Next.js]
    NextJS --> React[React]
    
    UILayer --> AntdMobile[Ant Design Mobile]
    UILayer --> TailwindCSS[TailwindCSS]
    UILayer --> StyledComponents[Styled Components]
    
    StateLayer --> Zustand[Zustand]
    
    NetworkLayer --> Axios[Axios]
    
    UtilityLayer --> TypeScript[TypeScript]
    UtilityLayer --> esToolkit[es-toolkit]
    UtilityLayer --> i18next[next-translate]

    AuthLayer --> NextAuth[next-auth]

    Monitoring[监控工具] --> Sentry[Sentry]

    Development[开发工具] --> ESLint[ESLint]
    Development --> Prettier[Prettier]
    Development --> Jest[Jest]
    Development --> NxTools[Nx工具]
    
    BuildTools[构建工具] --> Turbopack[Turbopack]
    BuildTools --> NxBuild[Nx构建系统]
    
    NxTools --> CodeGen[代码生成]
    NxTools --> ProjectGraph[依赖图分析]
    NxTools --> AffectedCommands[影响命令]
    
    NxBuild --> CacheSystem[缓存系统]
    NxBuild --> ParallelExecution[并行执行]
    NxBuild --> IncrementalBuild[增量构建]
    
    classDef frontendStack fill:#a8c0d6,stroke:#8fa5b7,stroke-width:2px,color:#2c3e50;
    classDef frameworkLayer fill:#b5add3,stroke:#9e95bb,stroke-width:2px,color:#2c3e50;
    classDef uiLayer fill:#d6c0b0,stroke:#b7a58f,stroke-width:2px,color:#3e342c;
    classDef stateLayer fill:#e8f0f7,stroke:#a8c0d6,stroke-width:2px,color:#3a4a5c;
    classDef networkLayer fill:#f7e8aa,stroke:#d9cb94,stroke-width:2px,color:#4d452d;
    classDef utilityLayer fill:#a8d1ce,stroke:#89b0ad,stroke-width:2px,color:#2d3e3d;
    classDef toolingLayer fill:#f9f9f9,stroke:#d9d9d9,stroke-width:2px,color:#2c3e50;
    classDef nxLayer fill:#cae3ca,stroke:#97c397,stroke-width:2px,color:#2e3e2e;
    
    class Frontend frontendStack;
    class Framework,NextJS,React frameworkLayer;
    class UILayer,AntdMobile,TailwindCSS,StyledComponents uiLayer;
    class StateLayer,Zustand,ReactContext,ImmerJS stateLayer;
    class NetworkLayer,Axios,SWR,FetchAPI networkLayer;
    class UtilityLayer,TypeScript,esToolkit,i18next,DateFNS utilityLayer;
    class Development,ESLint,Prettier,Jest,Cypress,BuildTools,Turbopack,Babel,PostCSS,SWC,Monitoring,Sentry,Analytics,Performance toolingLayer;
    class NxTools,ProjectGraph,AffectedCommands,CodeGen,NxBuild,CacheSystem,ParallelExecution,IncrementalBuild nxLayer;
```

## 5. Nx工作空间架构图

```mermaid
%%{init: {'theme': 'base', 'themeVariables': { 'primaryColor': '#cae3ca', 'primaryTextColor': '#2e3e2e', 'primaryBorderColor': '#97c397', 'lineColor': '#97c397', 'secondaryColor': '#a8d1ce', 'tertiaryColor': '#f9f9f9' }}}%%
graph TB
    Workspace[Nx工作空间] --> Apps[应用目录]
    Workspace --> Libs[库目录]
    Workspace --> Tools[工具目录]
    
    Apps --> MobileApp[移动应用]
    
    Libs --> Core[核心库]
    Libs --> Shared[共享库]
    Libs --> Feature[功能库]
    Libs --> UI[UI库]
    Libs --> Utils[工具库]
    
    Core --> CoreAuth[认证核心]
    Core --> CoreState[状态核心]
    Core --> CoreNetwork[网络核心]
    
    Shared --> SharedModels[共享模型]
    Shared --> SharedConfig[共享配置]
    Shared --> SharedTypes[共享类型]
    
    Feature --> FeatureTodo[待办功能]
    Feature --> FeatureForm[表单功能]
    Feature --> FeatureList[列表功能]
    
    UI --> UIComponents[UI组件]
    UI --> UITemplates[UI模板]
    UI --> UITheme[UI主题]
    
    Utils --> UtilsFormatters[格式化工具]
    Utils --> UtilsValidators[验证工具]
    Utils --> UtilsHelpers[辅助函数]
    
    MobileApp -->|依赖| Core
    MobileApp -->|依赖| Shared
    MobileApp -->|依赖| Feature
    MobileApp -->|依赖| UI
    
    Feature -->|依赖| Core
    Feature -->|依赖| Shared
    Feature -->|依赖| UI
    Feature -->|依赖| Utils
    
    UI -->|依赖| Utils
    UI -->|依赖| Shared
    
    classDef workspace fill:#cae3ca,stroke:#97c397,stroke-width:2px,color:#2e3e2e;
    classDef directory fill:#a8d1ce,stroke:#89b0ad,stroke-width:2px,color:#2d3e3d;
    classDef app fill:#b5add3,stroke:#9e95bb,stroke-width:2px,color:#2c3e50;
    classDef library fill:#d6c0b0,stroke:#b7a58f,stroke-width:2px,color:#3e342c;
    classDef specific fill:#e8f0f7,stroke:#a8c0d6,stroke-width:2px,color:#3a4a5c;
    
    class Workspace workspace;
    class Apps,Libs,Tools directory;
    class MobileApp app;
    class Core,Shared,Feature,UI,Utils library;
    class CoreAuth,CoreState,CoreNetwork,SharedModels,SharedConfig,SharedTypes,FeatureTodo,FeatureForm,FeatureList,UIComponents,UITemplates,UITheme,UtilsFormatters,UtilsValidators,UtilsHelpers specific;
```

## 6. 数据流向层级图（Nx优化）

```mermaid
%%{init: {'theme': 'base', 'themeVariables': { 'primaryColor': '#b5add3', 'primaryTextColor': '#FFF', 'primaryBorderColor': '#9e95bb', 'lineColor': '#a8d1ce', 'secondaryColor': '#a8d1ce', 'tertiaryColor': '#f9f9f9' }}}%%
graph TB
    User[用户] -->|交互| UI[用户界面]
    UI -->|事件| EventHandlers[事件处理器]
    EventHandlers -->|调用| StateActions[状态操作]
    
    StateActions -->|本地操作| LocalState[本地状态]
    StateActions -->|API请求| APILayer[API层]
    
    APILayer -->|请求| BackendAPI[后端API]
    BackendAPI -->|响应| APILayer
    
    APILayer -->|结果| StateActions
    StateActions -->|更新| GlobalState[全局状态]
    
    LocalState -->|渲染| UIComponents[UI组件]
    GlobalState -->|渲染| UIComponents
    UIComponents -->|展示| UI
    
    subgraph Nx库结构
        UIComponents --> UILibrary[UI库]
        EventHandlers --> FeatureLibrary[功能库]
        StateActions --> CoreStateLibrary[核心状态库]
        APILayer --> CoreNetworkLibrary[核心网络库]
        UILibrary --> SharedModelsLibrary[共享模型库]
        FeatureLibrary --> SharedModelsLibrary
    end
    
    subgraph 客户端数据流
        UI
        EventHandlers
        StateActions
        LocalState
        GlobalState
    end
    
    subgraph 服务端数据流
        APILayer
        BackendAPI
    end
    
    subgraph 状态同步流
        LocalState -.- GlobalState
    end
    
    classDef user fill:#e2a4a4,stroke:#d98c8c,stroke-width:2px,color:#4d3939;
    classDef uiComponents fill:#a8c0d6,stroke:#8fa5b7,stroke-width:2px,color:#2c3e50;
    classDef eventHandlers fill:#d6c0b0,stroke:#b7a58f,stroke-width:2px,color:#3e342c;
    classDef stateLayer fill:#b5add3,stroke:#9e95bb,stroke-width:2px,color:#2c3e50;
    classDef apiLayer fill:#f7e8aa,stroke:#d9cb94,stroke-width:2px,color:#4d452d;
    classDef backendLayer fill:#a8d1ce,stroke:#89b0ad,stroke-width:2px,color:#2d3e3d;
    classDef flowGroup fill:#f9f9f9,stroke:#d9d9d9,stroke-width:2px,color:#2c3e50;
    classDef nxLibrary fill:#cae3ca,stroke:#97c397,stroke-width:2px,color:#2e3e2e;
    
    class User user;
    class UI,UIComponents uiComponents;
    class EventHandlers eventHandlers;
    class StateActions,LocalState,GlobalState stateLayer;
    class APILayer apiLayer;
    class BackendAPI backendLayer;
    class 客户端数据流,服务端数据流,状态同步流 flowGroup;
    class UILibrary,FeatureLibrary,CoreStateLibrary,CoreNetworkLibrary,SharedModelsLibrary nxLibrary;
    class Nx库结构 nxLibrary;
```

## 总结

上述层级架构图从不同角度展示了移动端底座的架构设计，并集成了Nx单体仓库的管理方式：

1. **系统分层架构图**：展示了系统从用户到后端服务的整体层次结构，清晰地划分了表现层、业务逻辑层和数据访问层的关系。

2. **组件层级架构图**：详细描述了从应用根组件到各个细分组件的层级关系，包括布局、路由和各类UI组件。

3. **状态管理层级图**：阐明了基于Zustand的状态管理方案，包括全局状态、功能状态和UI状态的层次关系及持久化策略。

4. **技术栈层级图**：展示了整个技术栈的层次结构，新增了Nx相关的工具和构建系统，增强了开发和构建效率。

5. **Nx工作空间架构图**：展示了基于Nx的工作空间结构，将应用拆分为各种类型的库，实现更好的代码组织和复用。

6. **数据流向层级图**：描述了从用户交互到UI渲染的完整数据流向，并展示了Nx库结构如何与数据流向相结合。

这些层级架构图共同构成了基于Nx单体仓库的移动端底座的完整架构视图，为开发团队提供了清晰的架构指导，同时通过Nx提供的工具和最佳实践，提高了代码组织、开发效率和构建性能。
