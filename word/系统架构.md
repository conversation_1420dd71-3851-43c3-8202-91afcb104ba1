# 移动端底座系统架构（基于Nx单体仓库）

## 1. 系统全景架构图

```mermaid
%%{init: {'theme': 'base', 'themeVariables': { 'primaryColor': '#a8c0d6', 'primaryTextColor': '#2c3e50', 'primaryBorderColor': '#6d8cb0', 'lineColor': '#6d8cb0', 'secondaryColor': '#8fb0a9', 'tertiaryColor': '#f9f9f9' }}}%%
graph TB
    Client[客户端] -->|"HTTP/HTTPS"| LoadBalancer[负载均衡器]
    LoadBalancer -->|请求分发| NextJS[Next.js服务]
    
    subgraph 前端系统
        NextJS -->|渲染| SSR[服务端渲染]
        NextJS -->|静态生成| SSG[静态页面]
        NextJS -->|API路由| ServerAPI[API路由]
        NextJS -->|认证| Auth[认证服务]
        ServerAPI -->|业务| Services[业务服务]

        Auth -->|验证| IdentityProvider[身份提供者]
        
        subgraph 客户端渲染
            SSR --> Hydration[组件水合]
            SSG --> CSR[客户端交互]
            Hydration --> ClientComponents[客户端组件]
            CSR --> ClientComponents
        end
    end
    
    subgraph 后端系统
        Services -->|请求| BFF[后端API]
        BFF -->|数据| Database[数据服务]
        
        Services -->|读写| Database
    end
    
    subgraph 基础设施
        NextJS -->|监控| Monitoring[监控系统]
        NextJS -->|日志| Logging[日志系统]
        BFF -->|监控| Monitoring
        BFF -->|日志| Logging
        
        LoadBalancer -->|扩缩容| Autoscaling[自动伸缩]
        Autoscaling -->|管理| NextJS
    end
    
    classDef client fill:#e2a4a4,stroke:#d98c8c,stroke-width:2px,color:#4d3939;
    classDef infrastructure fill:#f7e8aa,stroke:#d9cb94,stroke-width:2px,color:#4d452d;
    classDef frontend fill:#a8d1ce,stroke:#89b0ad,stroke-width:2px,color:#2d3e3d;
    classDef backend fill:#b5add3,stroke:#9e95bb,stroke-width:2px,color:#2c3e50;
    classDef data fill:#b0c4d6,stroke:#8fa5b7,stroke-width:2px,color:#2c3e50;
    classDef clientside fill:#e8f0f7,stroke:#a8c0d6,stroke-width:2px,color:#3a4a5c;
    
    class Client client;
    class LoadBalancer,Monitoring,Logging,Backup,Autoscaling infrastructure;
    class NextJS,SSR,SSG,ServerAPI frontend;
    class BFF,Auth,Services,ExternalAPI backend;
    class Database data;
    class Hydration,CSR,ClientComponents clientside;
```

## 2. 系统逻辑分层架构

```mermaid
%%{init: {'theme': 'base', 'themeVariables': { 'primaryColor': '#a8c0d6', 'primaryTextColor': '#2c3e50', 'primaryBorderColor': '#6d8cb0', 'lineColor': '#6d8cb0', 'secondaryColor': '#8fb0a9', 'tertiaryColor': '#f9f9f9' }}}%%
graph TB
    User[用户] -->|交互| Presentation[表现层]
    
    subgraph 系统分层
        Presentation -->|依赖| BusinessLogic[业务逻辑层]
        BusinessLogic -->|依赖| DataAccess[数据访问层]
        DataAccess -->|依赖| Infrastructure[基础设施层]
    end
    
    subgraph 表现层
        UI[用户界面] --> WebUI[Web界面]
        UI --> Components[UI组件库]
        
        WebUI --> Responsive[响应式设计]
        Components --> AntdMobile[Antd Mobile]
    end
    
    subgraph 业务逻辑层
        ApplicationServices[应用服务] --> StateManagement[状态管理]
        ApplicationServices --> BizWorkflows[业务工作流]
    end
    
    subgraph 数据访问层
        Repository[仓储] --> APIClient[API客户端]
        Repository --> LocalStorage[本地存储]
        Repository --> Cache[缓存管理]
        
        APIClient --> REST[后端API]
    end
    
    subgraph 基础设施层
        Network[网络处理] --> Axios[Axios]
        
        Persistence[持久化] --> IndexedDB[IndexedDB]
        Persistence --> LocalForage[LocalForage]
        
        CrossCutting[横切关注点] --> Logging[日志]
        CrossCutting --> Monitoring[监控]
    end
    
    classDef user fill:#e2a4a4,stroke:#d98c8c,stroke-width:2px,color:#4d3939;
    classDef layer fill:#b0c4d6,stroke:#8fa5b7,stroke-width:2px,color:#2c3e50;
    classDef presentation fill:#a8d1ce,stroke:#89b0ad,stroke-width:2px,color:#2d3e3d;
    classDef business fill:#b5add3,stroke:#9e95bb,stroke-width:2px,color:#2c3e50;
    classDef data fill:#d6c0b0,stroke:#b7a58f,stroke-width:2px,color:#3e342c;
    classDef infra fill:#f7e8aa,stroke:#d9cb94,stroke-width:2px,color:#4d452d;
    classDef detail fill:#e8f0f7,stroke:#a8c0d6,stroke-width:2px,color:#3a4a5c;
    
    class User user;
    class Presentation,BusinessLogic,DataAccess,Infrastructure layer;
    class UI,MobileUI,WebUI,Components,ReactNative,Responsive,AntdMobile presentation;
    class Domain,ApplicationServices,Entities,UseCases,Services,StateManagement,BizWorkflows,Validation business;
    class Repository,APIClient,LocalStorage,Cache,REST,GraphQL data;
    class Network,Persistence,CrossCutting,HTTP,WebSocket,IndexedDB,LocalForage,Logging,Security,Monitoring infra;
```

## 3. 核心模块架构图

```mermaid
%%{init: {'theme': 'base', 'themeVariables': { 'primaryColor': '#b5add3', 'primaryTextColor': '#2c3e50', 'primaryBorderColor': '#9e95bb', 'lineColor': '#9e95bb', 'secondaryColor': '#d6c0b0', 'tertiaryColor': '#f9f9f9' }}}%%
graph TB
    Core[核心模块] --> Auth[认证授权模块]
    Core --> UI[UI组件模块]
    Core --> State[状态管理模块]
    Core --> Routing[路由导航模块]
    Core --> I18N[国际化模块]
    Core --> Utility[工具模块]
    Core --> PluginMgmt[插件管理模块]
    
    subgraph 认证授权模块
        Auth --> Login[登录认证]
        Auth --> Permission[权限管理]
        Auth --> Session[会话管理]
        
        Login --> WeChat[企业微信集成]
        Login --> OAuth[OAuth认证]
        
        Permission --> RBAC[角色权限控制]
        Permission --> ACL[访问控制列表]
    end
    
    subgraph UI组件模块
        UI --> Base[基础组件]
        UI --> Form[表单组件]
        UI --> List[列表组件]
        UI --> Chart[图表组件]
        UI --> Notification[通知组件]
        
        Base --> Layout[布局组件]
        Base --> Navigation[导航组件]
        
        Form --> FormTemplates[表单模板]
        List --> ListTemplates[列表模板]
    end
    
    subgraph 状态管理模块
        State --> GlobalState[全局状态]
        State --> LocalState[局部状态]
        State --> Persistence[持久化]
        
        GlobalState --> AuthStore[认证状态]
        GlobalState --> UIStore[UI状态]
        GlobalState --> FeatureStore[功能状态]
        
        LocalState --> ComponentState[组件状态]
        LocalState --> PageState[页面状态]
    end
    
    subgraph 路由导航模块
        Routing --> Routes[路由定义]
        Routing --> Guards[路由守卫]
        Routing --> Params[路由参数]
        
        Routes --> Static[静态路由]
        Routes --> Dynamic[动态路由]
        
        Guards --> AuthGuard[认证守卫]
        Guards --> RoleGuard[角色守卫]
    end

    subgraph 插件管理模块
        PluginMgmt --> PluginRegistry[插件注册表]
        PluginMgmt --> PluginLoader[插件加载器]
        PluginMgmt --> PluginLifecycle[插件生命周期管理]
        PluginMgmt --> HookSystem[钩子系统]
    end

    PluginMgmt -.->|影响| UI
    PluginMgmt -.->|影响| Routing
    PluginMgmt -.->|影响| State
    PluginMgmt -.->|影响| Auth
    
    classDef coreModule fill:#b5add3,stroke:#9e95bb,stroke-width:2px,color:#2c3e50;
    classDef moduleGroup fill:#e8f0f7,stroke:#a8c0d6,stroke-width:2px,color:#3a4a5c;
    classDef submodule fill:#a8d1ce,stroke:#89b0ad,stroke-width:2px,color:#2d3e3d;
    classDef feature fill:#d6c0b0,stroke:#b7a58f,stroke-width:2px,color:#3e342c;
    classDef pluginModule fill:#f9f9f9,stroke:#d9d9d9,stroke-width:2px,color:#2c3e50;
    
    class Core coreModule;
    class Auth,UI,State,Routing,I18N,Utility,PluginMgmt coreModule;
    class AuthModule,UIModule,StateModule,RoutingModule moduleGroup;
    class PluginManagementModule pluginModule;
    class Login,Permission,Session,Base,Form,List,Chart,Feedback,GlobalState,LocalState,Persistence,Routes,Guards,Params submodule;
    class WeChat,OAuth,RBAC,ACL,Layout,Navigation,FormTemplates,ListTemplates,AuthStore,UIStore,FeatureStore,ComponentState,PageState,Static,Dynamic,AuthGuard,RoleGuard feature;
    class PluginRegistry,PluginLoader,PluginLifecycle,HookSystem feature;

    %% 设置显示名称
    AuthModule["认证授权模块"]
    UIModule["UI组件模块"]
    StateModule["状态管理模块"]
    RoutingModule["路由导航模块"]
    PluginManagementModule["插件管理模块"]
```

## 4. Nx工作空间架构图

```mermaid
%%{init: {'theme': 'base', 'themeVariables': { 'primaryColor': '#cae3ca', 'primaryTextColor': '#2e3e2e', 'primaryBorderColor': '#97c397', 'lineColor': '#97c397', 'secondaryColor': '#a8d1ce', 'tertiaryColor': '#f9f9f9' }}}%%
graph TB
    subgraph Nx工作空间结构
        Workspace[Nx工作空间] --> Apps[应用]
        Workspace --> Libs[库]
        Workspace --> E2E[e2e测试]
        Workspace --> Tools[工具]
        
        Apps --> MobileApp[移动应用]
        
        Libs --> SharedLibs[共享库]
        Libs --> FeatureLibs[功能库]
        Libs --> UILibs[UI库]
        Libs --> DataLibs[数据库]
        Libs --> UtilLibs[工具库]
        
        SharedLibs --> SharedModels[共享模型]
        SharedLibs --> SharedConstants[共享常量]
        SharedLibs --> SharedInterfaces[共享接口]
        
        FeatureLibs --> AuthLib[认证库]
        FeatureLibs --> TodoLib[待办库]
        FeatureLibs --> FormLib[表单库]
        FeatureLibs --> ListLib[列表库]
        
        UILibs --> UIComponents[组件库]
        UILibs --> UITemplates[模板库]
        
        DataLibs --> DataAccess[数据访问]
        DataLibs --> DataState[数据状态]
        
        UtilLibs --> UtilHelpers[辅助函数]
        UtilLibs --> UtilFormatters[格式化工具]
        
        E2E --> MobileE2E[移动E2E测试]
        
        Tools --> Generators[代码生成器]
        Tools --> CodeMods[代码修改工具]
    end
    
    subgraph 项目依赖图
        MobileApp -->|依赖| FeatureLibs
        MobileApp -->|依赖| UILibs
        
        FeatureLibs -->|依赖| SharedLibs
        FeatureLibs -->|依赖| DataLibs
        FeatureLibs -->|依赖| UILibs
        
        UILibs -->|依赖| SharedLibs
        UILibs -->|依赖| UtilLibs
        
        DataLibs -->|依赖| SharedLibs
        DataLibs -->|依赖| UtilLibs
    end
    
    classDef workspace fill:#cae3ca,stroke:#97c397,stroke-width:2px,color:#2e3e2e;
    classDef mainDir fill:#a8d1ce,stroke:#89b0ad,stroke-width:2px,color:#2d3e3d;
    classDef libType fill:#b5add3,stroke:#9e95bb,stroke-width:2px,color:#2c3e50;
    classDef lib fill:#d6c0b0,stroke:#b7a58f,stroke-width:2px,color:#3e342c;
    classDef app fill:#e8f0f7,stroke:#a8c0d6,stroke-width:2px,color:#3a4a5c;
    classDef depGraph fill:#f9f9f9,stroke:#d9d9d9,stroke-width:2px,color:#2c3e50;
    
    class Workspace workspace;
    class Apps,Libs,E2E,Tools mainDir;
    class SharedLibs,FeatureLibs,UILibs,DataLibs,UtilLibs libType;
    class SharedModels,SharedConstants,SharedInterfaces,AuthLib,TodoLib,FormLib,ListLib,UIComponents,UITemplates,DataAccess,DataState,UtilHelpers,UtilFormatters lib;
    class MobileApp,MobileE2E,Generators,CodeMods app;
    class 项目依赖图 depGraph;
```

## 5. 部署架构图（集成Nx）

```mermaid
%%{init: {'theme': 'base', 'themeVariables': { 'primaryColor': '#a8c0d6', 'primaryTextColor': '#2c3e50', 'primaryBorderColor': '#6d8cb0', 'lineColor': '#6d8cb0', 'secondaryColor': '#8fb0a9', 'tertiaryColor': '#f9f9f9' }}}%%
graph TB
    Developer[开发人员] -->|提交代码| GitRepo[Git仓库]
    
    subgraph CI/CD流水线
        GitRepo -->|触发| NxAffected[Nx Affected]
        NxAffected -->|确定影响范围| Build[构建作业]
        Build -->|执行| Test[测试作业]
        Test -->|执行| Deploy[部署作业]
    end
    
    subgraph Nx缓存系统
        NxAffected -.->|使用| LocalCache[本地缓存]
        NxAffected -.->|使用| RemoteCache[远程缓存]
        Build -.->|使用| LocalCache
        Build -.->|使用| RemoteCache
        Test -.->|使用| LocalCache
        Test -.->|使用| RemoteCache
    end
    
    subgraph 环境
        Deploy -->|部署到| Dev[开发环境]
        Deploy -->|部署到| Sit[测试环境]
        Deploy -->|部署到| Uat[预发布环境]
        Deploy -->|部署到| Production[生产环境]
    end
    
    subgraph 生产基础设施
        Sit -->|部署| CDN[CDN]
        Uat -->|部署| CDN[CDN]
        Production -->|部署| CDN[CDN]

        
        Internet[互联网] -->|访问| CDN
        CDN -->|静态资源| Client[客户端]
        Client -->|动态请求| LoadBalancer[负载均衡器]
        LoadBalancer -->|分发| AppCluster
        AppCluster -->|读写| DBServers
        
        AppCluster -->|上报| Monitoring[监控系统]
        AppCluster -->|记录| Logging[日志系统]
        Monitoring -->|告警| OnCall[值班人员]
    end
    
    classDef human fill:#e2a4a4,stroke:#d98c8c,stroke-width:2px,color:#4d3939;
    classDef cicd fill:#b5add3,stroke:#9e95bb,stroke-width:2px,color:#2c3e50;
    classDef env fill:#a8d1ce,stroke:#89b0ad,stroke-width:2px,color:#2d3e3d;
    classDef infra fill:#d6c0b0,stroke:#b7a58f,stroke-width:2px,color:#3e342c;
    classDef external fill:#f7e8aa,stroke:#d9cb94,stroke-width:2px,color:#4d452d;
    classDef nxCache fill:#cae3ca,stroke:#97c397,stroke-width:2px,color:#2e3e2e;
    
    class Developer,OnCall human;
    class GitRepo,NxAffected,Build,Test,Deploy cicd;
    class Dev,Sit,Uat,Production env;
    class CDN,AppCluster,DBServers,LoadBalancer,Monitoring,Logging infra;
    class Internet,Client external;
    class LocalCache,RemoteCache,Nx缓存系统 nxCache;
```

## 6. 技术栈架构图（集成Nx）

```mermaid
%%{init: {'theme': 'base', 'themeVariables': { 'primaryColor': '#b5add3', 'primaryTextColor': '#2c3e50', 'primaryBorderColor': '#9e95bb', 'lineColor': '#9e95bb', 'secondaryColor': '#d6c0b0', 'tertiaryColor': '#f9f9f9' }}}%%
graph TB
    Mobile[移动端底座] --> Frontend[前端技术栈]
    Mobile --> DevOps[DevOps技术栈]
    
    subgraph 前端技术栈
        Frontend --> Framework[框架层]
        Frontend --> UI[UI层]
        Frontend --> State[状态管理层]
        Frontend --> Network[网络层]
        
        Framework --> NextJS[Next.js]
        NextJS --> React[React]
        NextJS --> TypeScript[TypeScript]
        
        UI --> AntdMobile[Ant Design Mobile]
        UI --> TailwindCSS[TailwindCSS]
        UI --> StyledComponents[Styled Components]
        
        State --> Zustand[Zustand]
        State --> Immer[Immer.js]
        
        Network --> Axios[Axios]
    end
  
    
    subgraph DevOps技术栈
        DevOps --> Version[版本控制]
        DevOps --> CI[持续集成]
        DevOps --> CD[持续部署]
        DevOps --> Monitoring[监控系统]
        
        Version --> Git[Git]
        
        CI --> GitHub[GitHub Actions]
        CI --> Jest[Jest测试]
        CI --> ESLint[ESLint]
        
        CD --> Vercel[Vercel]
        CD --> Docker[Docker]
        
        Monitoring --> Sentry[Sentry]
        Monitoring --> Analytics[分析工具]
    end
    
    subgraph Nx技术栈
        Mobile --> NxTools[Nx工具链]
        
        NxTools --> Workspace[工作空间管理]
        NxTools --> Generator[代码生成器]
        NxTools --> Executor[执行器]
        NxTools --> Dependency[依赖管理]
        NxTools --> Cache[缓存系统]
        
        Workspace --> WorkspaceConfig[工作空间配置]
        Workspace --> ProjectConfig[项目配置]
        
        Generator --> SchematicGen[自定义生成器]
        Generator --> Templates[模板系统]
        
        Executor --> TaskExecutor[任务执行器]
        Executor --> ParallelRun[并行运行]
        
        Dependency --> ProjectGraph[项目依赖图]
        Dependency --> TagsConstraints[标签约束]
        
        Cache --> LocalCache[本地缓存]
        Cache --> RemoteCache[远程缓存]
    end
    
    classDef main fill:#b5add3,stroke:#9e95bb,stroke-width:2px,color:#2c3e50;
    classDef frontendStack fill:#a8d1ce,stroke:#89b0ad,stroke-width:2px,color:#2d3e3d;
    classDef backendStack fill:#d6c0b0,stroke:#b7a58f,stroke-width:2px,color:#3e342c;
    classDef devopsStack fill:#e8f0f7,stroke:#a8c0d6,stroke-width:2px,color:#3a4a5c;
    classDef tech fill:#f7e8aa,stroke:#d9cb94,stroke-width:2px,color:#4d452d;
    classDef nxStack fill:#cae3ca,stroke:#97c397,stroke-width:2px,color:#2e3e2e;
    
    class Mobile main;
    class Frontend,Framework,UI,State,Network frontendStack;
    class Backend,APIGateway,Services,Database,Cache backendStack;
    class DevOps,Version,CI,CD,Monitoring devopsStack;
    class NextJS,React,TypeScript,AntdMobile,TailwindCSS,StyledComponents,Zustand,Immer,Axios,SWR,NextJSAPI,OAuth,WeChat,BusinessAPI,SQL,NoSQL,Redis,LocalCache,Git,GitHub,Jest,ESLint,Vercel,Docker,Sentry,Analytics tech;
    class NxTools,Workspace,Generator,Executor,Dependency,Cache,WorkspaceConfig,ProjectConfig,SchematicGen,Templates,TaskExecutor,ParallelRun,ProjectGraph,TagsConstraints,LocalCache,RemoteCache nxStack;
```

## 7. 系统交互时序图

```mermaid
%%{init: {'theme': 'base', 'themeVariables': { 'primaryColor': '#b5add3', 'primaryTextColor': '#FFF', 'primaryBorderColor': '#9e95bb', 'lineColor': '#a8d1ce', 'secondaryColor': '#a8d1ce', 'tertiaryColor': '#f9f9f9' }}}%%
sequenceDiagram
    participant User as 用户
    participant App as 移动应用
    participant State as 状态管理
    participant NextJS as Next.js服务
    participant API as API网关
    participant Auth as 认证服务
    participant BizService as 业务服务
    participant DB as 数据库
    
    User->>App: 1. 启动应用
    App->>NextJS: 2. 请求初始页面
    NextJS->>Auth: 3. 检查认证状态
    Auth-->>NextJS: 4. 返回认证结果
    
    alt 未认证
        NextJS-->>App: 5a. 返回登录页面
        App->>User: 6a. 显示登录页面
        User->>App: 7a. 输入凭据
        App->>Auth: 8a. 提交认证请求
        Auth->>DB: 9a. 验证凭据
        DB-->>Auth: 10a. 返回验证结果
        Auth-->>App: 11a. 返回认证结果
    else 已认证
        NextJS->>BizService: 5b. 请求初始数据
        BizService->>DB: 6b. 查询数据
        DB-->>BizService: 7b. 返回数据
        BizService-->>NextJS: 8b. 返回处理后数据
        NextJS-->>App: 9b. 返回渲染页面
    end
    
    App->>State: 12. 初始化状态
    App->>User: 13. 显示主页面
    
    User->>App: 14. 交互操作
    App->>State: 15. 更新本地状态
    App->>API: 16. 发送API请求
    API->>Auth: 17. 验证请求
    Auth-->>API: 18. 通过验证
    API->>BizService: 19. 转发业务请求
    BizService->>DB: 20. 处理数据
    DB-->>BizService: 21. 返回数据
    BizService-->>API: 22. 返回处理结果
    API-->>App: 23. 返回API响应
    App->>State: 24. 更新全局状态
    State-->>App: 25. 通知UI更新
    App->>User: 26. 更新界面展示
```

## 8. Nx依赖管理架构图

```mermaid
%%{init: {'theme': 'base', 'themeVariables': { 'primaryColor': '#cae3ca', 'primaryTextColor': '#2e3e2e', 'primaryBorderColor': '#97c397', 'lineColor': '#97c397', 'secondaryColor': '#a8d1ce', 'tertiaryColor': '#f9f9f9' }}}%%
graph TB
    subgraph Nx项目依赖图分析
        ProjectGraph[项目依赖图] --> DependencyAnalysis[依赖分析]
        ProjectGraph --> AffectedProjects[受影响项目]
        ProjectGraph --> CircularDeps[循环依赖检测]
        
        DependencyAnalysis --> StaticAnalysis[静态分析]
        DependencyAnalysis --> DynamicAnalysis[动态分析]
        
        StaticAnalysis --> ImportAnalysis[导入分析]
        StaticAnalysis --> TypeAnalysis[类型分析]
        
        DynamicAnalysis --> RuntimeDeps[运行时依赖]
        
        AffectedProjects --> ChangedFiles[文件变更]
        AffectedProjects --> TargetProjects[目标项目]
    end
    
    subgraph 依赖约束实施
        Constraints[依赖约束] --> Enforcers[约束实施器]
        Constraints --> Boundaries[边界定义]
        
        Enforcers --> LintRules[Lint规则]
        Enforcers --> BuildRules[构建规则]
        
        Boundaries --> Tags[项目标签]
        Boundaries --> Layers[架构分层]
        
        Tags --> UITag[ui标签]
        Tags --> FeatureTag[feature标签]
        Tags --> DataTag[data标签]
        Tags --> UtilTag[util标签]
        
        Layers --> PresentationLayer[表现层]
        Layers --> DomainLayer[领域层]
        Layers --> DataLayer[数据层]
    end
    
    subgraph 模块化策略
        ModuleStrategy[模块化策略] --> Granularity[粒度控制]
        ModuleStrategy --> Cohesion[内聚性]
        ModuleStrategy --> Coupling[耦合度]
        
        Granularity --> AtomicLibs[原子库]
        Granularity --> FeatureLibs[功能库]
        
        Cohesion --> SingleResp[单一职责]
        Cohesion --> SharedNothing[共享最小化]
        
        Coupling --> StrongBoundaries[强边界]
        Coupling --> ApiContracts[API契约]
    end
    
    ProjectGraph -->|指导| Constraints
    Constraints -->|实现| ModuleStrategy
    
    classDef graphAnalysis fill:#cae3ca,stroke:#97c397,stroke-width:2px,color:#2e3e2e;
    classDef analysisType fill:#a8d1ce,stroke:#89b0ad,stroke-width:2px,color:#2d3e3d;
    classDef specificAnalysis fill:#b5add3,stroke:#9e95bb,stroke-width:2px,color:#2c3e50;
    classDef constraints fill:#d6c0b0,stroke:#b7a58f,stroke-width:2px,color:#3e342c;
    classDef constraintImpl fill:#e8f0f7,stroke:#a8c0d6,stroke-width:2px,color:#3a4a5c;
    classDef moduleStrategy fill:#f7e8aa,stroke:#d9cb94,stroke-width:2px,color:#4d452d;
    classDef strategyImpl fill:#f9f9f9,stroke:#d9d9d9,stroke-width:2px,color:#2c3e50;
    
    class ProjectGraph,DependencyAnalysis,AffectedProjects,CircularDeps graphAnalysis;
    class StaticAnalysis,DynamicAnalysis analysisType;
    class ImportAnalysis,TypeAnalysis,RuntimeDeps,ChangedFiles,TargetProjects specificAnalysis;
    class Constraints,Enforcers,Boundaries constraints;
    class LintRules,BuildRules,Tags,Layers constraintImpl;
    class ModuleStrategy,Granularity,Cohesion,Coupling moduleStrategy;
    class AtomicLibs,FeatureLibs,SingleResp,SharedNothing,StrongBoundaries,ApiContracts strategyImpl;
    class UITag,FeatureTag,DataTag,UtilTag,PresentationLayer,DomainLayer,DataLayer constraintImpl;
```

## 总结

移动端底座系统架构基于Nx单体仓库实现，涵盖了前端到后端的完整技术体系：

1. **系统全景架构**展示了整体系统组成，包括客户端、Next.js服务集群、后端服务和基础设施等核心组件及其交互关系。

2. **系统逻辑分层架构**遵循经典的分层设计，清晰划分了表现层、业务逻辑层、数据访问层和基础设施层，每层具有明确的职责和边界。

3. **核心模块架构**详细描述了系统的六大核心模块：认证授权、UI组件、状态管理、路由导航、国际化和工具模块，及其内部组成。

4. **Nx工作空间架构**展示了基于Nx的工作空间结构和项目组织方式，实现了更清晰的代码组织和模块化。

5. **部署架构**展示了从开发到生产的完整流程，引入了Nx的增量构建和缓存系统，大幅提高了CI/CD效率。

6. **技术栈架构**全面呈现了系统采用的技术选型，新增了Nx工具链相关的技术，为代码管理提供了强大支持。

7. **系统交互时序图**通过典型用例展示了系统各组件间的交互流程，清晰描述了数据流动和状态变化的过程。

8. **Nx依赖管理架构**详细展示了Nx如何管理和约束项目依赖关系，确保架构的一致性和可维护性。

通过引入Nx单体仓库，移动端底座架构获得了以下优势：

- **高效的代码组织**：清晰的库划分和模块边界
- **增量构建和缓存**：显著提高构建速度和CI/CD效率
- **依赖管理和约束**：确保架构一致性，防止架构腐化
- **代码共享和复用**：最大化代码重用，避免重复开发
- **一致的开发体验**：统一的工具链和开发流程

这一架构设计确保了系统的高性能、可扩展性、可维护性和安全性，为移动应用开发提供了坚实的技术基础。
