# 移动端底座用户视觉架构（基于Nx）

## 1. 用户界面层次结构（Nx库结构）

```mermaid
%%{init: {'theme': 'base', 'themeVariables': { 'primaryColor': '#b5add3', 'primaryTextColor': '#2c3e50', 'primaryBorderColor': '#9e95bb', 'lineColor': '#9e95bb', 'secondaryColor': '#d6c0b0', 'tertiaryColor': '#f9f9f9' }}}%%
graph TD
    User[用户] --> |访问| App[移动应用]
    
    subgraph 应用视图层
        App --> Shell[应用外壳]
        Shell --> Navigation[导航系统]
        Shell --> Pages[页面容器]
        Shell --> Modals[模态对话框]
        Shell --> Notifications[通知提示]
        
        Navigation --> NavBar[导航栏]
        Navigation --> Tabs[底部标签栏]
        Navigation --> DrawerMenu[侧边抽屉菜单]
        
        Pages --> Auth[认证页面]
        Pages --> Dashboard[仪表盘页面]
        Pages --> Todo[代办页面]
        Pages --> Lists[列表页面]
        Pages --> Details[详情页面]
        Pages --> Charts[图表页面]
        
        Modals --> FormModals[表单弹窗]
        Modals --> ConfirmModals[确认弹窗]
        Modals --> InfoModals[信息弹窗]
    end
    
    subgraph Nx库结构
        App -.-> AppLib[应用库]
        Shell -.-> ShellLib[外壳库]
        Navigation -.-> NavigationLib[导航库]
        Pages -.-> PagesLib[页面库]
        Modals -.-> ModalsLib[模态框库]
        Notifications -.-> NotificationsLib[通知库]
        
        PagesLib -.-> AuthLib[认证库]
        PagesLib -.-> DashboardLib[仪表盘库]
        PagesLib -.-> TodoLib[代办库]
        PagesLib -.-> ListsLib[列表库]
        PagesLib -.-> DetailsLib[详情库]
        PagesLib -.-> ChartsLib[图表库]
    end
    
    subgraph 组件层
        Pages -.-> UIComponents[UI组件]
        Modals -.-> UIComponents
        
        UIComponents --> FormTemplates[表单模板]
        UIComponents --> ListTemplates[列表模板]
        UIComponents --> ChartTemplates[图表模板]
        
        FormTemplates --> FormItems[表单项组件]
        ListTemplates --> ListItems[列表项组件]
        
        FormItems --> BaseComponents[基础组件]
        ListItems --> BaseComponents
        ChartTemplates --> BaseComponents
    end
    
    subgraph UI组件库
        UIComponents -.-> UIComponentsLib[UI组件库]
        FormTemplates -.-> FormLib[表单库]
        ListTemplates -.-> ListLib[列表库]
        ChartTemplates -.-> ChartLib[图表库]
        BaseComponents -.-> BaseLib[基础组件库]
    end
    
    classDef user fill:#e2a4a4,stroke:#d98c8c,stroke-width:2px,color:#4d3939;
    classDef app fill:#a8d1ce,stroke:#89b0ad,stroke-width:2px,color:#2d3e3d;
    classDef viewLayer fill:#e8f0f7,stroke:#a8c0d6,stroke-width:2px,color:#3a4a5c;
    classDef navComponents fill:#d6c0b0,stroke:#b7a58f,stroke-width:2px,color:#3e342c;
    classDef pageComponents fill:#b0c4d6,stroke:#8fa5b7,stroke-width:2px,color:#2c3e50;
    classDef modalComponents fill:#f7e8aa,stroke:#d9cb94,stroke-width:2px,color:#4d452d;
    classDef uiComponents fill:#b5add3,stroke:#9e95bb,stroke-width:2px,color:#2c3e50;
    classDef baseComponents fill:#a8d1ce,stroke:#89b0ad,stroke-width:2px,color:#2d3e3d;
    classDef nxLib fill:#cae3ca,stroke:#97c397,stroke-width:2px,color:#2e3e2e;
    
    class User user;
    class App app;
    class Shell,Navigation,Pages,Modals,Notifications viewLayer;
    class NavBar,Tabs,DrawerMenu navComponents;
    class Auth,Dashboard,Todo,Lists,Details,Charts pageComponents;
    class FormModals,ConfirmModals,InfoModals modalComponents;
    class UIComponents,FormTemplates,ListTemplates,ChartTemplates uiComponents;
    class FormItems,ListItems,BaseComponents baseComponents;
    class AppLib,ShellLib,NavigationLib,PagesLib,ModalsLib,NotificationsLib,AuthLib,DashboardLib,TodoLib,ListsLib,DetailsLib,ChartsLib,UIComponentsLib,FormLib,ListLib,ChartLib,BaseLib nxLib;
    class Nx库结构,UI组件库 nxLib;
```

## 2. 页面导航流程

```mermaid
%%{init: {'theme': 'base', 'themeVariables': { 'primaryColor': '#a8c0d6', 'primaryTextColor': '#2c3e50', 'primaryBorderColor': '#8fa5b7', 'lineColor': '#8fa5b7', 'secondaryColor': '#d6c0b0', 'tertiaryColor': '#f9f9f9' }}}%%
graph LR
    Login[登录页面] --> |认证成功| Home[首页/仪表盘]
    
    Home --> |导航| Todo[代办模块]
    Home --> |导航| Module1[模块1]
    Home --> |导航| Module2[模块2]
    Home --> |导航| Settings[设置页面]
    
    Todo --> |查看详情| TodoDetail[代办详情]
    TodoDetail --> |返回| Todo
    
    Module1 --> |查看列表| List1[列表页面]
    Module1 --> |查看详情| Detail1[详情页面]
    
    List1 --> |选择项| Detail1
    Detail1 --> |返回| List1
    
    Module2 --> |查看列表| List2[列表页面]
    Module2 --> |查看详情| Detail2[详情页面]
    Module2 --> |查看图表| Chart[图表页面]
    
    List2 --> |选择项| Detail2
    Detail2 --> |返回| List2
    Chart --> |返回| Module2
    
    classDef entry fill:#e2a4a4,stroke:#d98c8c,stroke-width:2px,color:#4d3939;
    classDef main fill:#a8d1ce,stroke:#89b0ad,stroke-width:2px,color:#2d3e3d;
    classDef module fill:#b5add3,stroke:#9e95bb,stroke-width:2px,color:#2c3e50;
    classDef page fill:#d6c0b0,stroke:#b7a58f,stroke-width:2px,color:#3e342c;
    
    class Login entry;
    class Home main;
    class Todo,Module1,Module2,Settings module;
    class TodoDetail,List1,Detail1,List2,Detail2,Chart page;
```

## 3. 交互流程图

```mermaid
%%{init: {'theme': 'base', 'themeVariables': { 'primaryColor': '#b5add3', 'primaryTextColor': '#FFF', 'primaryBorderColor': '#9e95bb', 'lineColor': '#a8d1ce', 'secondaryColor': '#a8d1ce', 'tertiaryColor': '#f9f9f9' }}}%%
sequenceDiagram
    participant User as 用户
    participant UI as 界面
    participant State as 状态管理
    participant API as API服务
    
    User->>UI: 进入应用
    UI->>State: 初始化状态
    State->>API: 请求数据
    API-->>State: 返回数据
    State-->>UI: 更新视图
    UI-->>User: 展示内容
    
    User->>UI: 交互操作
    UI->>State: 触发状态变更
    
    alt 本地操作
        State-->>UI: 直接更新视图
        UI-->>User: 展示反馈
    else 需要网络请求
        State->>API: 发送请求
        UI-->>User: 显示加载状态
        API-->>State: 返回结果
        State-->>UI: 更新视图
        UI-->>User: 展示结果和反馈
    end
    
    Note over UI,State: 使用zustand管理状态
    Note over State,API: 使用AxiosHook/AxiosTools处理API请求
```

## 4. 主题与视觉系统（Nx库划分）

```mermaid
%%{init: {'theme': 'base', 'themeVariables': { 'primaryColor': '#a8c0d6', 'primaryTextColor': '#2c3e50', 'primaryBorderColor': '#8fa5b7', 'lineColor': '#8fa5b7', 'secondaryColor': '#d6c0b0', 'tertiaryColor': '#f9f9f9' }}}%%
graph TD
    Theme[主题系统] --> Colors[色彩系统]
    Theme --> Spacing[间距系统]
    Theme --> Components[组件主题]
    
    Colors --> Primary[主色调]
    Colors --> Secondary[辅助色]
    Colors --> Neutral[中性色]
    Colors --> Functional[功能色]
    
    Primary --> Brand[品牌色]
    Secondary --> Accent[强调色]
    Neutral --> Background[背景色]
    Neutral --> Text[文本色]
    Functional --> Success[成功色]
    Functional --> Warning[警告色]
    Functional --> Error[错误色]
    Functional --> Info[信息色]
    
    Components --> FormTheme[表单主题]
    Components --> ListTheme[列表主题]
    Components --> ChartTheme[图表主题]
    Components --> NavTheme[导航主题]
    
    subgraph Nx主题库结构
        Theme -.-> ThemeLib[主题库]
        Colors -.-> ColorsLib[色彩库]
        Spacing -.-> SpacingLib[间距库]
        Components -.-> ComponentThemeLib[组件主题库]
        
        ThemeLib --> ThemeProvider[主题提供者]
        ThemeLib --> ThemeHooks[主题钩子]
        ThemeLib --> ThemeContext[主题上下文]
        
        ColorsLib --> ColorTokens[色彩令牌]
        ColorsLib --> ColorUtils[色彩工具]
        
        SpacingLib --> SpacingTokens[间距令牌]
        SpacingLib --> SpacingUtils[间距工具]
        
        ComponentThemeLib --> ComponentThemes[组件主题集]
        ComponentThemeLib --> ThemeOverrides[主题覆盖]
    end
    
    subgraph 响应式适配
        MediaQueries[媒体查询] --> MobileLayout[移动布局]
        MediaQueries --> TabletLayout[平板布局]
    
    end
    
    classDef themeSystem fill:#b5add3,stroke:#9e95bb,stroke-width:2px,color:#2c3e50;
    classDef colorSystem fill:#a8d1ce,stroke:#89b0ad,stroke-width:2px,color:#2d3e3d;
    classDef colorCategory fill:#d6c0b0,stroke:#b7a58f,stroke-width:2px,color:#3e342c;
    classDef specificColor fill:#f7e8aa,stroke:#d9cb94,stroke-width:2px,color:#4d452d;
    classDef componentTheme fill:#e8f0f7,stroke:#a8c0d6,stroke-width:2px,color:#3a4a5c;
    classDef responsive fill:#f9f9f9,stroke:#d9d9d9,stroke-width:2px,color:#2c3e50;
    classDef nxLib fill:#cae3ca,stroke:#97c397,stroke-width:2px,color:#2e3e2e;
    
    class Theme themeSystem;
    class Colors,Spacing,Components themeSystem;
    class Primary,Secondary,Neutral,Functional colorSystem;
    class Brand,Accent,Background,Text,Success,Warning,Error,Info specificColor;
    class FormTheme,ListTheme,ChartTheme,NavTheme componentTheme;
    class MediaQueries,MobileLayout,TabletLayout,DesktopLayout responsive;
    class ThemeLib,ColorsLib,SpacingLib,ComponentThemeLib,ThemeProvider,ThemeHooks,ThemeContext,ColorTokens,ColorUtils,SpacingTokens,SpacingUtils,ComponentThemes,ThemeOverrides nxLib;
    class Nx主题库结构 nxLib;
```

## 5. 用户反馈系统

```mermaid
%%{init: {'theme': 'base', 'themeVariables': { 'primaryColor': '#b5add3', 'primaryTextColor': '#2c3e50', 'primaryBorderColor': '#9e95bb', 'lineColor': '#9e95bb', 'secondaryColor': '#d6c0b0', 'tertiaryColor': '#f9f9f9' }}}%%
graph TD
    Feedback[用户反馈系统] --> Loading[加载状态]
    Feedback --> Notification[通知提示]
    Feedback --> ValidationFeedback[表单验证反馈]
    Feedback --> ErrorHandling[错误处理]
    
    Loading --> Skeleton[骨架屏]
    Loading --> Spinner[加载动画]
    Loading --> ProgressBar[进度条]
    
    Notification --> Toast[轻提示]
    Notification --> Alert[警告框]
    Notification --> Banner[横幅通知]
    Notification --> Badge[徽标]
    
    ValidationFeedback --> InlineError[内联错误]
    ValidationFeedback --> FieldHighlight[字段高亮]
    ValidationFeedback --> FormMessage[表单消息]
    
    ErrorHandling --> NetworkError[网络错误]
    ErrorHandling --> ValidationError[验证错误]
    ErrorHandling --> SystemError[系统错误]
    ErrorHandling --> RecoveryAction[恢复操作]
    
    classDef feedbackSystem fill:#b5add3,stroke:#9e95bb,stroke-width:2px,color:#2c3e50;
    classDef feedbackCategory fill:#a8d1ce,stroke:#89b0ad,stroke-width:2px,color:#2d3e3d;
    classDef feedbackComponent fill:#d6c0b0,stroke:#b7a58f,stroke-width:2px,color:#3e342c;
    
    class Feedback feedbackSystem;
    class Loading,Notification,ValidationFeedback,ErrorHandling feedbackCategory;
    class Skeleton,Spinner,ProgressBar,Toast,Alert,Banner,Badge,InlineError,FieldHighlight,FormMessage,NetworkError,ValidationError,SystemError,RecoveryAction feedbackComponent;
```

## 6. Nx应用结构图

```mermaid
%%{init: {'theme': 'base', 'themeVariables': { 'primaryColor': '#cae3ca', 'primaryTextColor': '#2e3e2e', 'primaryBorderColor': '#97c397', 'lineColor': '#97c397', 'secondaryColor': '#a8d1ce', 'tertiaryColor': '#f9f9f9' }}}%%
graph TB
    subgraph Nx应用结构
        App[应用入口] --> AppModule[应用模块]
        
        AppModule --> ShellModule[外壳模块]
        AppModule --> CoreModule[核心模块]
        AppModule --> SharedModule[共享模块]
        AppModule --> FeatureModules[功能模块]
        
        ShellModule --> AppShell[应用外壳]
        ShellModule --> AppLayout[应用布局]
        ShellModule --> AppNavigation[应用导航]
        
        CoreModule --> AuthService[认证服务]
        CoreModule --> StateService[状态服务]
        CoreModule --> APIService[API服务]
        CoreModule --> LoggerService[日志服务]
        
        SharedModule --> UIModule[UI模块]
        SharedModule --> UtilsModule[工具模块]
        
        FeatureModules --> TodoModule[待办模块]
        FeatureModules --> FormModule[表单模块]
        FeatureModules --> ListModule[列表模块]
        FeatureModules --> ChartModule[图表模块]
        
        UIModule --> BaseComponents[基础组件]
        UIModule --> CompoundComponents[复合组件]
        
        BaseComponents --> Button[按钮]
        BaseComponents --> Input[输入框]
        BaseComponents --> Icon[图标]
        
        CompoundComponents --> DataTable[数据表格]
        CompoundComponents --> FormComponent[表单组件]
        CompoundComponents --> CardComponent[卡片组件]
    end
    
    subgraph Nx库依赖关系
        ShellModule -.->|依赖| CoreModule
        ShellModule -.->|依赖| SharedModule
        
        FeatureModules -.->|依赖| CoreModule
        FeatureModules -.->|依赖| SharedModule
        
        TodoModule -.->|依赖| UIModule
        FormModule -.->|依赖| UIModule
        ListModule -.->|依赖| UIModule
        ChartModule -.->|依赖| UIModule
        
        UIModule -.->|依赖| UtilsModule
    end
    
    classDef appEntry fill:#cae3ca,stroke:#97c397,stroke-width:2px,color:#2e3e2e;
    classDef module fill:#a8d1ce,stroke:#89b0ad,stroke-width:2px,color:#2d3e3d;
    classDef feature fill:#b5add3,stroke:#9e95bb,stroke-width:2px,color:#2c3e50;
    classDef component fill:#d6c0b0,stroke:#b7a58f,stroke-width:2px,color:#3e342c;
    classDef service fill:#e8f0f7,stroke:#a8c0d6,stroke-width:2px,color:#3a4a5c;
    classDef baseComp fill:#f7e8aa,stroke:#d9cb94,stroke-width:2px,color:#4d452d;
    classDef depends fill:#f9f9f9,stroke:#d9d9d9,stroke-width:2px,color:#2c3e50;
    
    class App,AppModule appEntry;
    class ShellModule,CoreModule,SharedModule,FeatureModules,UIModule,UtilsModule module;
    class TodoModule,FormModule,ListModule,ChartModule feature;
    class AppShell,AppLayout,AppNavigation,BaseComponents,CompoundComponents component;
    class AuthService,StateService,APIService,LoggerService service;
    class Button,Input,Icon,DataTable,FormComponent,CardComponent baseComp;
    class Nx库依赖关系 depends;
```

## 总结

以上视觉架构图从用户体验和视觉设计的角度描述了基于Nx单体仓库的移动端底座应用的整体结构。这些图表展示了用户如何与应用交互、导航流程、主题系统以及反馈机制，为前端开发提供了清晰的视觉指导。

通过引入Nx工作空间和库结构，视觉架构获得了以下优势：

1. **模块化的UI组件**：UI组件被划分为独立的库，便于维护和复用
2. **主题系统的分层管理**：主题相关功能被组织成独立的库，支持主题定制和切换
3. **清晰的应用结构**：应用结构遵循Nx的最佳实践，分为外壳、核心、共享和功能模块
4. **优化的依赖关系**：通过Nx的依赖管理，确保UI组件和功能模块之间的依赖关系清晰合理

该架构基于Next.js、Ant Design Mobile、Zustand等技术栈构建，结合Nx单体仓库的管理方式，遵循了现代移动应用的设计原则和最佳实践，为开发团队提供了一致且高效的开发体验。
