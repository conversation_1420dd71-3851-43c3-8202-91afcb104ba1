import './globals.css';
import 'antd-mobile/es/global';
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'TZ移动端应用',
  description: '移动端表单应用',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN">
      <body className="pb-14">
        <div className="min-h-screen bg-gray-50">
          {children}
        </div>
      </body>
    </html>
  );
}
